# Business Enhancement Configuration
# Copy these settings to your .env file and adjust as needed

# Enable/disable automatic enhancement scheduling
BUSINESS_ENHANCEMENT_SCHEDULE_ENABLED=true

# Batch processing settings (keep small for production safety)
BUSINESS_ENHANCEMENT_BATCH_SIZE=3
BUSINESS_ENHANCEMENT_DELAY=45
BUSINESS_ENHANCEMENT_MAX_RUNTIME=300

# System load protection
BUSINESS_ENHANCEMENT_MAX_DB_CONN=30
BUSINESS_ENHANCEMENT_MAX_QUEUE=15
BUSINESS_ENHANCEMENT_MAX_JOBS=2

# Scheduling (cron format or frequency in minutes)
# Every 30 minutes during business hours (9 AM - 6 PM)
BUSINESS_ENHANCEMENT_CRON="*/30 9-18 * * *"
# Alternative: run every X minutes
# BUSINESS_ENHANCEMENT_FREQUENCY=45

# Safety limits
BUSINESS_ENHANCEMENT_DAILY_LIMIT=500
BUSINESS_ENHANCEMENT_COOLDOWN=24

# Queue settings
BUSINESS_ENHANCEMENT_QUEUE=business-enhancement
BUSINESS_ENHANCEMENT_QUEUE_CONNECTION=database
BUSINESS_ENHANCEMENT_JOB_TIMEOUT=300
BUSINESS_ENHANCEMENT_JOB_RETRIES=2

# Monitoring and logging
BUSINESS_ENHANCEMENT_LOG=true
BUSINESS_ENHANCEMENT_LOG_LEVEL=info
BUSINESS_ENHANCEMENT_NOTIFICATIONS=false
BUSINESS_ENHANCEMENT_NOTIFICATION_EMAILS=""
BUSINESS_ENHANCEMENT_SLACK_WEBHOOK=""
