<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Business extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    protected $fillable = [
        'business_uuid',
        'name',
        'category',
        'location',
        'email',
        'phone',
        'address',
        'logo',
        'banner',
        'latitude',
        'longitude',
        'lat',
        'lng',
        'about',
        'tax_id',
        'registration_number',
        'website',
        'social_media',
        'business_hours',
        'hours',
        'photos',
        'services',
        'reviews',
        'status',
    ];

    protected $casts = [
        'social_media' => 'array',
        'business_hours' => 'array',
        'hours' => 'array',
        'photos' => 'array',
        'services' => 'array',
        'reviews' => 'array',
    ];

    public static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->business_uuid = (string) Str::uuid();
        });
    }

    /**
     * Get the certificate assets for the business.
     */
    public function certificateAssets()
    {
        return $this->morphMany(CertificateAsset::class, 'certifiable');
    }

    /**
     * Get the providers associated with the business.
     */
    public function providers(): HasMany
    {
        return $this->hasMany(User::class, 'business_uuid', 'business_uuid');
    }

    /**
     * Get the active subscription for the business.
     */
    public function activeSubscription(): HasOne
    {
        return $this->hasOne(BusinessSubscription::class, 'business_uuid', 'business_uuid')
            ->where('is_active', true);
    }

    /**
     * Get all subscriptions for the business.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(BusinessSubscription::class, 'business_uuid', 'business_uuid');
    }

    /**
     * Get the job notification recipients for the business.
     */
    public function jobNotificationRecipients(): HasMany
    {
        return $this->hasMany(JobNotificationRecipient::class);
    }

    /**
     * Scope a query to only include active businesses.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Check if business has complete data for miles search feature
     *
     * @return bool
     */
    public function hasCompleteData(): bool
    {
        $completenessService = app(\App\Services\BusinessDataCompletenessService::class);
        return $completenessService->isBusinessDataComplete($this);
    }

    /**
     * Get missing critical fields for this business
     *
     * @return array
     */
    public function getMissingCriticalFields(): array
    {
        $completenessService = app(\App\Services\BusinessDataCompletenessService::class);
        return $completenessService->getMissingCriticalFields($this);
    }



    /**
     * Check if business can receive job notifications
     *
     * @return bool
     */
    public function canReceiveNotifications(): bool
    {
        return !empty($this->email) && filter_var($this->email, FILTER_VALIDATE_EMAIL);
    }

    /**
     * Check if business has valid coordinates for distance calculation
     *
     * @return bool
     */
    public function hasValidCoordinates(): bool
    {
        return !empty($this->lat) && !empty($this->lng)
            && is_numeric($this->lat) && is_numeric($this->lng)
            && $this->lat >= -90 && $this->lat <= 90
            && $this->lng >= -180 && $this->lng <= 180;
    }

    /**
     * Check if business has a valid address with zip code
     *
     * @return bool
     */
    public function hasValidAddress(): bool
    {
        return !empty($this->address) && preg_match('/\b\d{5}\b/', $this->address) === 1;
    }

    /**
     * Scope to filter businesses with complete data
     */
    public function scopeWithCompleteData($query)
    {
        return $query->whereNotNull('email')
                    ->whereNotNull('lat')
                    ->whereNotNull('lng')
                    ->whereNotNull('address')
                    ->whereNotNull('name')
                    ->where('email', '!=', '')
                    ->where('lat', '!=', '')
                    ->where('lng', '!=', '')
                    ->where('address', '!=', '')
                    ->where('name', '!=', '');
    }

    /**
     * Scope to filter businesses with incomplete data
     */
    public function scopeWithIncompleteData($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('email')
              ->orWhereNull('lat')
              ->orWhereNull('lng')
              ->orWhereNull('address')
              ->orWhereNull('name')
              ->orWhere('email', '')
              ->orWhere('lat', '')
              ->orWhere('lng', '')
              ->orWhere('address', '')
              ->orWhere('name', '');
        });
    }
}