<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ScrapingJob extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'job_notification_campaign_id',
        'location_query',
        'category_query',
        'status',
        'progress',
        'discovered_businesses_count',
        'message',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'progress' => 'array',
    ];

    /**
     * Get the job notification campaign that owns the scraping job.
     */
    public function jobNotificationCampaign(): BelongsTo
    {
        return $this->belongsTo(JobNotificationCampaign::class);
    }
} 