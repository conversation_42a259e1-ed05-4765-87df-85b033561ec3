<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Enums\JobNotificationStatusEnum;
use Carbon\Carbon;
use Illuminate\Support\Str;

class JobNotificationCampaign extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'job_id',
        'job_title',
        'job_description',
        'job_budget',
        'job_zip_code',
        'job_address',
        'job_latitude',
        'job_longitude',
        'job_category',
        'customer_name',
        'customer_email',
        'customer_phone',
        'search_radius',
        'business_count',
        'sent_count',
        'failed_count',
        'status',
        'admin_token',
        'token_expires_at',
        'approved_at',
        'rejected_at',
        'rejection_reason',
        'sent_at',
        'event_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'job_budget' => 'decimal:2',
        'search_radius' => 'float',
        'business_count' => 'integer',
        'token_expires_at' => 'datetime',
        'approved_at' => 'datetime',
        'rejected_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the recipients for the campaign.
     */
    public function recipients(): HasMany
    {
        return $this->hasMany(JobNotificationRecipient::class);
    }

    /**
     * Get the scraping job for the campaign.
     */
    public function scrapingJob(): HasOne
    {
        return $this->hasOne(ScrapingJob::class);
    }

    /**
     * Scope a query to only include pending campaigns.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include approved campaigns.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include rejected campaigns.
     */
    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    /**
     * Scope a query to only include sent campaigns.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Check if the campaign is pending approval.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the campaign is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Check if the campaign is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === 'rejected';
    }

    /**
     * Check if the campaign is sent.
     */
    public function isSent(): bool
    {
        return $this->status === 'sent';
    }

    /**
     * Check if the approval token is expired.
     */
    public function isTokenExpired(): bool
    {
        return $this->token_expires_at->isPast();
    }

    /**
     * Generate a new admin token for campaign approval/rejection.
     *
     * @param int $expiryHours
     * @return string
     */
    public function generateAdminToken($expiryHours = null)
    {
        // Get expiry hours from config or use the provided value
        $expiryHours = $expiryHours ?? config('job_notification.token_expiry_hours', 48);
        
        // Generate a secure random token
        $token = Str::random(64);
        
        // Set token and expiration
        $this->admin_token = $token;
        $this->token_expires_at = Carbon::now()->addHours($expiryHours);
        $this->save();
        
        return $token;
    }
} 