<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\ScrapingJobResource;
use App\Models\ScrapingJob;
use App\Jobs\ScrapeBusinessesJob;
use App\Enums\ScrapingJobStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ScrapingJobController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // TODO: Add authorization
        $query = ScrapingJob::with('jobNotificationCampaign');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('campaign_id')) {
            $query->where('job_notification_campaign_id', $request->input('campaign_id'));
        }

        if ($request->filled('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('location_query', 'like', '%' . $search . '%')
                  ->orWhere('category_query', 'like', '%' . $search . '%')
                  ->orWhere('message', 'like', '%' . $search . '%')
                  ->orWhereHas('jobNotificationCampaign', function ($campaignQuery) use ($search) {
                      $campaignQuery->where('job_title', 'like', '%' . $search . '%')
                                   ->orWhere('job_id', 'like', '%' . $search . '%');
                  });
            });
        }

        // Pagination with limit and page parameters (matching user preferences)
        $limit = $request->input('limit', 15);
        $page = $request->input('page', 1);

        // Validate limit and page
        $limit = max(1, min(100, (int) $limit)); // Between 1 and 100
        $page = max(1, (int) $page);

        // Set current page for Laravel paginator
        \Illuminate\Pagination\Paginator::currentPageResolver(function () use ($page) {
            return $page;
        });

        $scrapingJobs = $query->orderByDesc('created_at')->paginate($limit, ['*'], 'page', $page);

        return ScrapingJobResource::collection($scrapingJobs);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\ScrapingJob  $scrapingJob
     * @return \Illuminate\Http\Response
     */
    public function show(ScrapingJob $scrapingJob)
    {
        // TODO: Add authorization
        $scrapingJob->load('jobNotificationCampaign');
        return new ScrapingJobResource($scrapingJob);
    }

    /**
     * Cancel a running scraping job.
     *
     * @param  \App\Models\ScrapingJob  $scrapingJob
     * @return \Illuminate\Http\Response
     */
    public function cancel(ScrapingJob $scrapingJob)
    {
        // TODO: Add authorization

        if (!in_array($scrapingJob->status, [ScrapingJobStatusEnum::PENDING, ScrapingJobStatusEnum::IN_PROGRESS])) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot cancel a scraping job that is not pending or in progress.',
                'current_status' => $scrapingJob->status
            ], 400);
        }

        try {
            $scrapingJob->update([
                'status' => ScrapingJobStatusEnum::FAILED,
                'message' => 'Cancelled by admin request'
            ]);

            // Update the related campaign status
            $campaign = $scrapingJob->jobNotificationCampaign;
            $campaign->update([
                'status' => \App\Enums\JobNotificationStatusEnum::REJECTED,
                'rejection_reason' => 'Scraping job was cancelled by admin'
            ]);

            Log::info('Scraping job cancelled by admin', [
                'scraping_job_id' => $scrapingJob->id,
                'campaign_id' => $campaign->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Scraping job has been cancelled successfully.',
                'data' => new ScrapingJobResource($scrapingJob->fresh('jobNotificationCampaign'))
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to cancel scraping job', [
                'scraping_job_id' => $scrapingJob->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel scraping job: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Retry a failed scraping job.
     *
     * @param  \App\Models\ScrapingJob  $scrapingJob
     * @return \Illuminate\Http\Response
     */
    public function retry(ScrapingJob $scrapingJob)
    {
        // TODO: Add authorization

        if ($scrapingJob->status !== ScrapingJobStatusEnum::FAILED) {
            return response()->json([
                'success' => false,
                'message' => 'Can only retry failed scraping jobs.',
                'current_status' => $scrapingJob->status
            ], 400);
        }

        try {
            // Reset the scraping job status
            $scrapingJob->update([
                'status' => ScrapingJobStatusEnum::PENDING,
                'message' => 'Retrying scraping job by admin request',
                'discovered_businesses_count' => 0
            ]);

            // Update the related campaign status
            $campaign = $scrapingJob->jobNotificationCampaign;
            $campaign->update([
                'status' => \App\Enums\JobNotificationStatusEnum::PENDING_SCRAPING,
                'rejection_reason' => null
            ]);

            // Dispatch the scraping job again
            ScrapeBusinessesJob::dispatch($scrapingJob);

            Log::info('Scraping job retried by admin', [
                'scraping_job_id' => $scrapingJob->id,
                'campaign_id' => $campaign->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Scraping job has been queued for retry.',
                'data' => new ScrapingJobResource($scrapingJob->fresh('jobNotificationCampaign'))
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retry scraping job', [
                'scraping_job_id' => $scrapingJob->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retry scraping job: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get the status and progress of a scraping job.
     *
     * @param  \App\Models\ScrapingJob  $scrapingJob
     * @return \Illuminate\Http\Response
     */
    public function status(ScrapingJob $scrapingJob)
    {
        // TODO: Add authorization

        $scrapingJob->load('jobNotificationCampaign');

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $scrapingJob->id,
                'status' => $scrapingJob->status,
                'message' => $scrapingJob->message,
                'progress' => $scrapingJob->progress,
                'discovered_businesses_count' => $scrapingJob->discovered_businesses_count,
                'created_at' => $scrapingJob->created_at,
                'updated_at' => $scrapingJob->updated_at,
                'campaign' => [
                    'id' => $scrapingJob->jobNotificationCampaign->id,
                    'job_id' => $scrapingJob->jobNotificationCampaign->job_id,
                    'job_title' => $scrapingJob->jobNotificationCampaign->job_title,
                    'status' => $scrapingJob->jobNotificationCampaign->status,
                ]
            ]
        ]);
    }
}
