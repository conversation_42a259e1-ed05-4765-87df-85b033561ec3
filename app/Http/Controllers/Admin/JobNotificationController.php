<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\JobNotificationCampaign;
use App\Jobs\ProcessJobNotificationJob;
use App\Enums\JobNotificationStatusEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class JobNotificationController extends Controller
{
    /**
     * Display a listing of job notification campaigns.
     */
    public function index(Request $request)
    {
        $campaigns = JobNotificationCampaign::with('recipients')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.job-notifications.index', compact('campaigns'));
    }

    /**
     * Display the specified campaign.
     */
    public function show(JobNotificationCampaign $campaign)
    {
        $campaign->load('recipients');

        return view('admin.job-notifications.show', compact('campaign'));
    }

    /**
     * Approve a job notification campaign.
     */
    public function approve(JobNotificationCampaign $campaign, string $token)
    {
        $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app/admin'), '/');
        return redirect()->away($webAppDomain . '/campaign-approval/' . $campaign->id . '/' . $token);
    }

    /**
     * Show rejection form.
     */
    public function rejectForm(JobNotificationCampaign $campaign, string $token)
    {
        $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app/admin'), '/');
        return redirect()->away($webAppDomain . '/campaign-approval/' . $campaign->id . '/' . $token . '?reject=1');
    }

    /**
     * Reject a job notification campaign.
     */
    public function reject(Request $request, JobNotificationCampaign $campaign, string $token)
    {
        // Verify token
        if ($campaign->admin_token !== $token) {
            abort(403, 'Invalid rejection token');
        }

        // Check if token is expired
        if ($campaign->token_expires_at->isPast()) {
            abort(403, 'Rejection token has expired');
        }

        // Validate rejection reason
        $request->validate([
            'rejection_reason' => 'required|string|max:500',
        ]);

        try {
            // Update campaign status
            $campaign->update([
                'status' => JobNotificationStatusEnum::REJECTED,
                'rejected_at' => now(),
                'rejection_reason' => $request->rejection_reason,
            ]);

            Log::info('Campaign rejected', [
                'campaign_id' => $campaign->id,
                'rejection_reason' => $request->rejection_reason,
                'rejected_by' => 'admin_token',
            ]);

            // Redirect to web app instead of returning blade view
            $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app/admin'), '/');
            return redirect()->away($webAppDomain . '/campaign-approval/' . $campaign->id . '/' . $token);

        } catch (\Exception $e) {
            Log::error('Error rejecting campaign', [
                'campaign_id' => $campaign->id,
                'error' => $e->getMessage(),
            ]);

            // Redirect to web app with error handling
            $webAppDomain = rtrim(env('JOBON_WEBAPP_URL', 'https://jobon.app/admin'), '/');
            return redirect()->away($webAppDomain . '/campaign-approval/' . $campaign->id . '/' . $token . '?error=rejection_failed');
        }
    }
} 