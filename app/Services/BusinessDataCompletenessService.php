<?php

namespace App\Services;

use App\Models\Business;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class BusinessDataCompletenessService
{
    /**
     * Critical fields required for the miles search feature and job notifications
     */
    const CRITICAL_FIELDS = [
        'email' => 'required_for_notifications',
        'lat' => 'required_for_distance_calculation',
        'lng' => 'required_for_distance_calculation',
        'address' => 'required_for_zip_code_generation',
        'name' => 'required_for_identification',
    ];

    /**
     * Optional but important fields for better business data
     */
    const IMPORTANT_FIELDS = [
        'phone' => 'contact_information',
        'website' => 'business_information',
        'category' => 'job_matching',
    ];

    /**
     * Check if a business has complete data for the miles search feature
     *
     * @param Business $business
     * @return bool
     */
    public function isBusinessDataComplete(Business $business): bool
    {
        $missingFields = $this->getMissingCriticalFields($business);
        return empty($missingFields);
    }

    /**
     * Get missing critical fields for a business
     *
     * @param Business $business
     * @return array Array of missing field names
     */
    public function getMissingCriticalFields(Business $business): array
    {
        $missingFields = [];

        foreach (array_keys(self::CRITICAL_FIELDS) as $field) {
            if (!$this->isFieldValid($business, $field)) {
                $missingFields[] = $field;
            }
        }

        return $missingFields;
    }

    /**
     * Get missing important (optional) fields for a business
     *
     * @param Business $business
     * @return array Array of missing field names
     */
    public function getMissingImportantFields(Business $business): array
    {
        $missingFields = [];

        foreach (array_keys(self::IMPORTANT_FIELDS) as $field) {
            if (!$this->isFieldValid($business, $field)) {
                $missingFields[] = $field;
            }
        }

        return $missingFields;
    }

    /**
     * Check if a specific field is valid for a business
     *
     * @param Business $business
     * @param string $field
     * @return bool
     */
    public function isFieldValid(Business $business, string $field): bool
    {
        $value = $business->{$field};

        switch ($field) {
            case 'email':
                return !empty($value) && filter_var($value, FILTER_VALIDATE_EMAIL);
            
            case 'lat':
            case 'lng':
                return !empty($value) && is_numeric($value) && $this->isValidCoordinate($value, $field);
            
            case 'address':
                return !empty($value) && $this->hasValidZipCode($value);
            
            case 'name':
            case 'phone':
            case 'website':
            case 'category':
                return !empty($value) && trim($value) !== '';
            
            default:
                return !empty($value);
        }
    }

    /**
     * Check if coordinate is within valid range
     *
     * @param float $coordinate
     * @param string $type 'lat' or 'lng'
     * @return bool
     */
    private function isValidCoordinate(float $coordinate, string $type): bool
    {
        if ($type === 'lat') {
            return $coordinate >= -90 && $coordinate <= 90;
        } elseif ($type === 'lng') {
            return $coordinate >= -180 && $coordinate <= 180;
        }
        
        return false;
    }

    /**
     * Check if address contains a valid US zip code
     *
     * @param string $address
     * @return bool
     */
    private function hasValidZipCode(string $address): bool
    {
        // Check for 5-digit zip code pattern
        return preg_match('/\b\d{5}\b/', $address) === 1;
    }

    /**
     * Filter businesses that have complete data
     *
     * @param Collection $businesses
     * @return Collection
     */
    public function filterCompleteBusinesses(Collection $businesses): Collection
    {
        return $businesses->filter(function ($business) {
            return $this->isBusinessDataComplete($business);
        });
    }

    /**
     * Filter businesses that have incomplete data
     *
     * @param Collection $businesses
     * @return Collection
     */
    public function filterIncompleteBusinesses(Collection $businesses): Collection
    {
        return $businesses->filter(function ($business) {
            return !$this->isBusinessDataComplete($business);
        });
    }

    /**
     * Get businesses that need data enhancement
     *
     * @param string $zipCode
     * @param string|null $category
     * @param float|null $radius
     * @return Collection Collection of businesses with missing data info
     */
    public function getBusinessesNeedingEnhancement(string $zipCode, ?string $category = null, ?float $radius = null): Collection
    {
        // Use BusinessDiscoveryService to find businesses in the area
        $businessDiscoveryService = app(BusinessDiscoveryService::class);
        $businesses = $businessDiscoveryService->findBusinesses($zipCode, $category, $radius);

        // Filter to only incomplete businesses and add missing field info
        return $this->filterIncompleteBusinesses($businesses)->map(function ($business) {
            $business->missing_critical_fields = $this->getMissingCriticalFields($business);
            $business->missing_important_fields = $this->getMissingImportantFields($business);
            return $business;
        });
    }

    /**
     * Generate a completeness report for a business
     *
     * @param Business $business
     * @return array
     */
    public function generateCompletenessReport(Business $business): array
    {
        $missingCritical = $this->getMissingCriticalFields($business);
        $missingImportant = $this->getMissingImportantFields($business);
        
        return [
            'business_id' => $business->id,
            'business_name' => $business->name,
            'is_complete' => empty($missingCritical),
            'completeness_score' => $this->calculateCompletenessScore($business),
            'missing_critical_fields' => $missingCritical,
            'missing_important_fields' => $missingImportant,
            'can_receive_notifications' => $this->isFieldValid($business, 'email'),
            'can_calculate_distance' => $this->isFieldValid($business, 'lat') && $this->isFieldValid($business, 'lng'),
            'has_valid_zip_code' => !empty($business->zip_code),
        ];
    }

    /**
     * Calculate completeness score (0-100)
     *
     * @param Business $business
     * @return int
     */
    public function calculateCompletenessScore(Business $business): int
    {
        $totalFields = count(self::CRITICAL_FIELDS) + count(self::IMPORTANT_FIELDS);
        $completeFields = 0;

        // Check critical fields (weighted more heavily)
        foreach (array_keys(self::CRITICAL_FIELDS) as $field) {
            if ($this->isFieldValid($business, $field)) {
                $completeFields += 2; // Critical fields count double
            }
        }

        // Check important fields
        foreach (array_keys(self::IMPORTANT_FIELDS) as $field) {
            if ($this->isFieldValid($business, $field)) {
                $completeFields += 1;
            }
        }

        $maxScore = (count(self::CRITICAL_FIELDS) * 2) + count(self::IMPORTANT_FIELDS);
        return (int) round(($completeFields / $maxScore) * 100);
    }

    /**
     * Log business completeness analysis
     *
     * @param Collection $businesses
     * @param string $context
     * @return void
     */
    public function logCompletenessAnalysis(Collection $businesses, string $context = 'general'): void
    {
        $complete = $this->filterCompleteBusinesses($businesses);
        $incomplete = $this->filterIncompleteBusinesses($businesses);

        Log::info('Business data completeness analysis', [
            'context' => $context,
            'total_businesses' => $businesses->count(),
            'complete_businesses' => $complete->count(),
            'incomplete_businesses' => $incomplete->count(),
            'completeness_percentage' => $businesses->count() > 0 
                ? round(($complete->count() / $businesses->count()) * 100, 2) 
                : 0,
        ]);

        if ($incomplete->isNotEmpty()) {
            $fieldAnalysis = [];
            foreach ($incomplete as $business) {
                $missing = $this->getMissingCriticalFields($business);
                foreach ($missing as $field) {
                    $fieldAnalysis[$field] = ($fieldAnalysis[$field] ?? 0) + 1;
                }
            }

            Log::info('Most common missing critical fields', [
                'context' => $context,
                'missing_field_counts' => $fieldAnalysis,
            ]);
        }
    }
}
