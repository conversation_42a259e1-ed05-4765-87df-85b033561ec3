<?php

namespace App\Services;

use App\Models\Business;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class BusinessDataCompletenessService
{
    /**
     * Critical fields required for the miles search feature and job notifications
     */
    const CRITICAL_FIELDS = [
        'email' => 'required_for_notifications',
        'lat' => 'required_for_distance_calculation',
        'lng' => 'required_for_distance_calculation',
        'address' => 'required_for_zip_code_generation',
        'name' => 'required_for_identification',
    ];



    /**
     * Check if a business has complete data for the miles search feature
     *
     * @param Business $business
     * @return bool
     */
    public function isBusinessDataComplete(Business $business): bool
    {
        $missingFields = $this->getMissingCriticalFields($business);
        return empty($missingFields);
    }

    /**
     * Get missing critical fields for a business
     *
     * @param Business $business
     * @return array Array of missing field names
     */
    public function getMissingCriticalFields(Business $business): array
    {
        $missingFields = [];

        foreach (array_keys(self::CRITICAL_FIELDS) as $field) {
            if (!$this->isFieldValid($business, $field)) {
                $missingFields[] = $field;
            }
        }

        return $missingFields;
    }



    /**
     * Check if a specific field is valid for a business
     *
     * @param Business $business
     * @param string $field
     * @return bool
     */
    public function isFieldValid(Business $business, string $field): bool
    {
        $value = $business->{$field};

        switch ($field) {
            case 'email':
                return !empty($value) && filter_var($value, FILTER_VALIDATE_EMAIL);
            
            case 'lat':
            case 'lng':
                return !empty($value) && is_numeric($value) && $this->isValidCoordinate($value, $field);
            
            case 'address':
                return !empty($value) && $this->hasValidZipCode($value);
            
            case 'name':
            case 'phone':
            case 'website':
            case 'category':
                return !empty($value) && trim($value) !== '';
            
            default:
                return !empty($value);
        }
    }

    /**
     * Check if coordinate is within valid range
     *
     * @param float $coordinate
     * @param string $type 'lat' or 'lng'
     * @return bool
     */
    private function isValidCoordinate(float $coordinate, string $type): bool
    {
        if ($type === 'lat') {
            return $coordinate >= -90 && $coordinate <= 90;
        } elseif ($type === 'lng') {
            return $coordinate >= -180 && $coordinate <= 180;
        }
        
        return false;
    }

    /**
     * Check if address contains a valid US zip code
     *
     * @param string $address
     * @return bool
     */
    private function hasValidZipCode(string $address): bool
    {
        // Check for 5-digit zip code pattern
        return preg_match('/\b\d{5}\b/', $address) === 1;
    }

    /**
     * Filter businesses that have complete data
     *
     * @param Collection $businesses
     * @return Collection
     */
    public function filterCompleteBusinesses(Collection $businesses): Collection
    {
        return $businesses->filter(function ($business) {
            return $this->isBusinessDataComplete($business);
        });
    }

    /**
     * Filter businesses that have incomplete data
     *
     * @param Collection $businesses
     * @return Collection
     */
    public function filterIncompleteBusinesses(Collection $businesses): Collection
    {
        return $businesses->filter(function ($business) {
            return !$this->isBusinessDataComplete($business);
        });
    }


}
