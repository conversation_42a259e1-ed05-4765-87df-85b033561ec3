<?php

namespace App\Services;

use App\Models\Business;
use App\Models\ScrapingJob;
use App\Services\JobNotificationService;
use Illuminate\Support\Facades\Log;

class ScrapingService
{
    protected $jobNotificationService;

    public function __construct(JobNotificationService $jobNotificationService)
    {
        $this->jobNotificationService = $jobNotificationService;
    }

    /**
     * Scrape businesses for a given scraping job.
     *
     * @param ScrapingJob $scrapingJob
     * @return void
     */
    public function scrape(ScrapingJob $scrapingJob): void
    {
        Log::info('Starting to scrape businesses for scraping job', ['scraping_job_id' => $scrapingJob->id]);

        // This is a placeholder for the actual scraping logic.
        // The logic from index.js needs to be converted to PHP here.
        // This would likely involve using a headless browser solution like Panther
        // or a robust scraping library.

        // For now, we will simulate finding a few businesses.
        $this->simulateScraping($scrapingJob);

        // Once scraping is done and businesses are added, we need to
        // re-trigger the notification process for the original campaign.
        $this->processCampaignAfterScraping($scrapingJob);
    }

    /**
     * Simulate scraping and creating businesses.
     *
     * @param ScrapingJob $scrapingJob
     */
    protected function simulateScraping(ScrapingJob $scrapingJob): void
    {
        $locationQuery = $scrapingJob->location_query;
        $categoryQuery = $scrapingJob->category_query;

        // Simulate finding 2 businesses using the same structure as ImportBusinessesJob
        $discoveredCount = 0;

        for ($i = 1; $i <= 2; $i++) {
            try {
                Business::create([
                    'name' => "Scraped Business $i from $locationQuery",
                    'address' => "$locationQuery, Sample Address $i",
                    'phone' => "******-000-000$i",
                    'website' => "https://scraped-business-$i.com",
                    'category' => $categoryQuery,
                    'location' => "$locationQuery",
                    'email' => "business$<EMAIL>",
                    'hours' => [
                        'monday' => ['open' => '09:00', 'close' => '17:00'],
                        'tuesday' => ['open' => '09:00', 'close' => '17:00'],
                        'wednesday' => ['open' => '09:00', 'close' => '17:00'],
                        'thursday' => ['open' => '09:00', 'close' => '17:00'],
                        'friday' => ['open' => '09:00', 'close' => '17:00'],
                        'saturday' => ['open' => '10:00', 'close' => '15:00'],
                        'sunday' => ['open' => 'closed', 'close' => 'closed']
                    ],
                    'photos' => [
                        "https://via.placeholder.com/640x480.png/business-$i-1",
                        "https://via.placeholder.com/640x480.png/business-$i-2"
                    ],
                    'services' => [
                        'Professional Service',
                        'Quality Work'
                    ],
                    'reviews' => [
                        [
                            'rating' => 5,
                            'comment' => 'Great service from scraped business!',
                            'author' => 'Satisfied Customer',
                            'date' => date('Y-m-d')
                        ]
                    ],
                    'lat' => '40.7128',
                    'lng' => '-74.0060',
                ]);
                $discoveredCount++;
            } catch (\Exception $e) {
                Log::error('Failed to create scraped business', [
                    'scraping_job_id' => $scrapingJob->id,
                    'business_index' => $i,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Scraping completed successfully', [
            'scraping_job_id' => $scrapingJob->id,
            'location_query' => $locationQuery,
            'category_query' => $categoryQuery,
            'discovered_businesses_count' => $discoveredCount
        ]);

        $scrapingJob->update([
            'discovered_businesses_count' => $discoveredCount,
            'status' => \App\Enums\ScrapingJobStatusEnum::COMPLETED,
            'message' => "Simulated scraping completed successfully. Found $discoveredCount businesses."
        ]);

        Log::info('Simulated scraping finished.', ['scraping_job_id' => $scrapingJob->id]);
    }

    /**
     * Process the job notification campaign after scraping is complete.
     *
     * @param ScrapingJob $scrapingJob
     */
    protected function processCampaignAfterScraping(ScrapingJob $scrapingJob): void
    {
        $campaign = $scrapingJob->jobNotificationCampaign;

        if ($scrapingJob->discovered_businesses_count > 0) {
            Log::info('Re-processing job notification campaign after successful scraping.', [
                'campaign_id' => $campaign->id,
                'discovered_businesses_count' => $scrapingJob->discovered_businesses_count
            ]);

            // Use the JobNotificationService to re-run business discovery
            // This will find the newly scraped businesses and set up the campaign
            $businessesFound = $this->jobNotificationService->processBusinessDiscovery(
                $campaign,
                $campaign->job_zip_code,
                $campaign->job_category,
                $campaign->search_radius
            );

            if ($businessesFound) {
                Log::info('Campaign successfully re-processed after scraping', [
                    'campaign_id' => $campaign->id,
                    'business_count' => $campaign->business_count
                ]);
            } else {
                Log::warning('No businesses found during re-processing after scraping', [
                    'scraping_job_id' => $scrapingJob->id,
                    'campaign_id' => $campaign->id,
                ]);
                $campaign->status = \App\Enums\JobNotificationStatusEnum::REJECTED;
                $campaign->rejection_reason = 'No businesses found even after scraping and re-processing.';
                $campaign->save();
            }
        } else {
            Log::warning('Scraping finished but no businesses were found.', [
                'scraping_job_id' => $scrapingJob->id,
                'campaign_id' => $campaign->id,
            ]);
            $campaign->status = \App\Enums\JobNotificationStatusEnum::REJECTED;
            $campaign->rejection_reason = 'No businesses found even after scraping.';
            $campaign->save();
        }
    }
} 