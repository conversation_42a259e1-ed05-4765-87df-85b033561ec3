<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [

    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->call('App\Http\Controllers\API\CommissionHistoryController@store');
        $schedule->call('App\Http\Controllers\BookingController@reminder')->daily();

        // Business Enhancement - Safe automatic enhancement
        if (config('business_enhancement.schedule.enabled', true)) {
            $enhancementCommand = $schedule->command('businesses:enhance-sequential')
                ->withoutOverlapping(config('business_enhancement.schedule.timeout', 600))
                ->runInBackground()
                ->appendOutputTo(storage_path('logs/business_enhancement.log'));

            // Use custom cron if specified, otherwise default frequency
            if ($cron = config('business_enhancement.schedule.cron')) {
                $enhancementCommand->cron($cron);
            } else {
                $frequency = config('business_enhancement.schedule.frequency_minutes', 30);
                $enhancementCommand->everyMinutes($frequency);
            }

            // Add different priority runs throughout the day
            $schedule->command('businesses:enhance-sequential --priority=email --batch-size=3 --delay=45')
                ->hourly()
                ->between('9:00', '18:00') // Business hours only
                ->withoutOverlapping()
                ->runInBackground()
                ->appendOutputTo(storage_path('logs/business_enhancement_email.log'));

            // Coordinates enhancement during low traffic hours
            $schedule->command('businesses:enhance-sequential --priority=coordinates --batch-size=5 --delay=30')
                ->twiceDaily(2, 14) // 2 AM and 2 PM
                ->withoutOverlapping()
                ->runInBackground()
                ->appendOutputTo(storage_path('logs/business_enhancement_coordinates.log'));
        }
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
