<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Business;
use App\Services\BusinessDataCompletenessService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class BusinessEnhancementStatus extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'businesses:enhancement-status 
                            {--detailed : Show detailed breakdown by field}
                            {--export= : Export results to file}';

    /**
     * The console command description.
     */
    protected $description = 'Show current status of business data enhancement system';

    /**
     * Execute the console command.
     */
    public function handle(BusinessDataCompletenessService $completenessService)
    {
        $this->info('🔍 Business Data Enhancement Status Report');
        $this->info('=' . str_repeat('=', 50));

        // System Status
        $this->showSystemStatus();
        $this->line('');

        // Overall Statistics
        $this->showOverallStats($completenessService);
        $this->line('');

        // Field-specific breakdown
        if ($this->option('detailed')) {
            $this->showDetailedBreakdown($completenessService);
            $this->line('');
        }

        // Queue Status
        $this->showQueueStatus();
        $this->line('');

        // Recent Activity
        $this->showRecentActivity();

        // Export if requested
        if ($exportFile = $this->option('export')) {
            $this->exportReport($completenessService, $exportFile);
        }
    }

    /**
     * Show system status
     */
    private function showSystemStatus()
    {
        $this->info('📊 System Status:');
        
        $isRunning = Cache::has('business_enhancement_progress_running');
        $lastProcessedId = Cache::get('business_enhancement_progress_last_id', 0);
        
        $this->line("  Enhancement Process: " . ($isRunning ? '🟢 Running' : '🔴 Stopped'));
        $this->line("  Last Processed ID: {$lastProcessedId}");
        
        // Check if scheduler is enabled
        $scheduleEnabled = config('business_enhancement.schedule.enabled', false);
        $this->line("  Auto Schedule: " . ($scheduleEnabled ? '🟢 Enabled' : '🔴 Disabled'));
        
        // Check queue worker
        $queueWorkerRunning = $this->isQueueWorkerRunning();
        $this->line("  Queue Worker: " . ($queueWorkerRunning ? '🟢 Running' : '🔴 Not Running'));
    }

    /**
     * Show overall statistics
     */
    private function showOverallStats(BusinessDataCompletenessService $completenessService)
    {
        $this->info('📈 Overall Statistics:');
        
        $totalBusinesses = Business::count();
        $completeBusinesses = Business::withCompleteData()->count();
        $incompleteBusinesses = Business::withIncompleteData()->count();
        
        $completenessPercentage = $totalBusinesses > 0 
            ? round(($completeBusinesses / $totalBusinesses) * 100, 2) 
            : 0;

        $this->line("  Total Businesses: {$totalBusinesses}");
        $this->line("  Complete Data: {$completeBusinesses} ({$completenessPercentage}%)");
        $this->line("  Incomplete Data: {$incompleteBusinesses}");
        
        // Progress bar for visual representation
        $this->line('');
        $this->line('  Completeness Progress:');
        $progressBar = $this->output->createProgressBar(100);
        $progressBar->setProgress($completenessPercentage);
        $progressBar->display();
        $this->line('');
    }

    /**
     * Show detailed breakdown by field
     */
    private function showDetailedBreakdown(BusinessDataCompletenessService $completenessService)
    {
        $this->info('🔍 Detailed Field Analysis:');
        
        $fields = ['email', 'lat', 'lng', 'address', 'phone', 'website'];
        $fieldStats = [];
        
        foreach ($fields as $field) {
            $missing = Business::where(function ($query) use ($field) {
                $query->whereNull($field)->orWhere($field, '');
            })->count();
            
            $fieldStats[$field] = $missing;
        }
        
        // Sort by most missing
        arsort($fieldStats);
        
        $headers = ['Field', 'Missing Count', 'Priority'];
        $rows = [];
        
        foreach ($fieldStats as $field => $count) {
            $priority = $this->getFieldPriority($field);
            $rows[] = [$field, $count, $priority];
        }
        
        $this->table($headers, $rows);
    }

    /**
     * Show queue status
     */
    private function showQueueStatus()
    {
        $this->info('🚀 Queue Status:');
        
        try {
            $totalJobs = DB::table('jobs')->count();
            $enhancementJobs = DB::table('jobs')
                ->where('payload', 'like', '%EnhanceBusinessDataJob%')
                ->count();
            $failedJobs = DB::table('failed_jobs')->count();
            
            $this->line("  Total Queued Jobs: {$totalJobs}");
            $this->line("  Enhancement Jobs: {$enhancementJobs}");
            $this->line("  Failed Jobs: {$failedJobs}");
            
            if ($enhancementJobs > 10) {
                $this->warn("  ⚠️  High number of enhancement jobs in queue");
            }
            
        } catch (\Exception $e) {
            $this->warn("  ⚠️  Could not retrieve queue status: " . $e->getMessage());
        }
    }

    /**
     * Show recent activity
     */
    private function showRecentActivity()
    {
        $this->info('📅 Recent Activity (Last 24 hours):');
        
        try {
            // This would require adding timestamps to track enhancements
            // For now, show recent business updates
            $recentUpdates = Business::where('updated_at', '>=', now()->subDay())
                ->count();
            
            $this->line("  Recent Business Updates: {$recentUpdates}");
            
            // Show recent failed jobs
            $recentFailures = DB::table('failed_jobs')
                ->where('failed_at', '>=', now()->subDay())
                ->where('payload', 'like', '%EnhanceBusinessDataJob%')
                ->count();
            
            if ($recentFailures > 0) {
                $this->warn("  ⚠️  Recent Enhancement Failures: {$recentFailures}");
            } else {
                $this->line("  Enhancement Failures: 0 ✅");
            }
            
        } catch (\Exception $e) {
            $this->warn("  ⚠️  Could not retrieve recent activity: " . $e->getMessage());
        }
    }

    /**
     * Export report to file
     */
    private function exportReport(BusinessDataCompletenessService $completenessService, string $filename)
    {
        $this->info("📄 Exporting report to: {$filename}");
        
        $report = [
            'timestamp' => now()->toISOString(),
            'total_businesses' => Business::count(),
            'complete_businesses' => Business::withCompleteData()->count(),
            'incomplete_businesses' => Business::withIncompleteData()->count(),
            'field_analysis' => $this->getFieldAnalysis(),
            'queue_status' => $this->getQueueStatusArray(),
        ];
        
        file_put_contents($filename, json_encode($report, JSON_PRETTY_PRINT));
        $this->info("✅ Report exported successfully!");
    }

    /**
     * Check if queue worker is running
     */
    private function isQueueWorkerRunning(): bool
    {
        // Simple check - this might need adjustment based on your setup
        $output = shell_exec('ps aux | grep "queue:work" | grep -v grep');
        return !empty($output);
    }

    /**
     * Get field priority
     */
    private function getFieldPriority(string $field): string
    {
        $priorities = config('business_enhancement.priorities', []);
        
        foreach ($priorities as $priority => $config) {
            if (in_array($field, $config['fields'] ?? [])) {
                return ucfirst($priority) . " (Weight: {$config['weight']})";
            }
        }
        
        return 'Low';
    }

    /**
     * Get field analysis array
     */
    private function getFieldAnalysis(): array
    {
        $fields = ['email', 'lat', 'lng', 'address', 'phone', 'website'];
        $analysis = [];
        
        foreach ($fields as $field) {
            $missing = Business::where(function ($query) use ($field) {
                $query->whereNull($field)->orWhere($field, '');
            })->count();
            
            $analysis[$field] = $missing;
        }
        
        return $analysis;
    }

    /**
     * Get queue status as array
     */
    private function getQueueStatusArray(): array
    {
        try {
            return [
                'total_jobs' => DB::table('jobs')->count(),
                'enhancement_jobs' => DB::table('jobs')
                    ->where('payload', 'like', '%EnhanceBusinessDataJob%')
                    ->count(),
                'failed_jobs' => DB::table('failed_jobs')->count(),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
