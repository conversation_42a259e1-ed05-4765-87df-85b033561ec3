<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Business;
use App\Jobs\EnhanceBusinessDataJob;
use App\Services\BusinessDataCompletenessService;
use Illuminate\Support\Facades\Log;

class EnhanceBusinessesSequentially extends Command
{

    protected $signature = 'businesses:enhance-sequential
                            {--batch-size=10 : Number of businesses to process per batch}
                            {--priority=email : Priority field to focus on (email, coordinates, address, all)}
                            {--dry-run : Show what would be enhanced without actually doing it}';


    protected $description = 'Enhance businesses with missing critical fields in batches';




    public function handle(BusinessDataCompletenessService $completenessService)
    {
        $batchSize = $this->option('batch-size');
        $priority = $this->option('priority');
        $isDryRun = $this->option('dry-run');

        $this->info("🚀 Starting business enhancement");
        $this->info("📊 Settings: Batch={$batchSize}, Priority={$priority}");

        if ($isDryRun) {
            $this->warn("🔍 DRY RUN MODE - No actual changes will be made");
        }

        try {
            // Get businesses to process
            $businesses = $this->getBusinessesToEnhance($priority, $batchSize);

            if ($businesses->isEmpty()) {
                $this->info("✅ No businesses found that need enhancement for priority: {$priority}");
                return 0;
            }

            $this->line("🔄 Processing {$businesses->count()} businesses...");

            $totalEnhanced = 0;
            foreach ($businesses as $business) {
                $missingFields = $completenessService->getMissingCriticalFields($business);

                if (!empty($missingFields)) {
                    $priorityFields = $this->getPriorityFields($missingFields, $priority);

                    if (!empty($priorityFields)) {
                        if ($isDryRun) {
                            $this->line("  🔍 Would enhance: {$business->name} (ID: {$business->id}) - Fields: " . implode(', ', $priorityFields));
                        } else {
                            // Dispatch with small delay to prevent queue flooding
                            EnhanceBusinessDataJob::dispatch($business, $priorityFields)
                                ->delay(now()->addSeconds(rand(5, 30)));

                            $this->line("  ✨ Queued: {$business->name} (ID: {$business->id}) - Fields: " . implode(', ', $priorityFields));
                            $totalEnhanced++;
                        }
                    }
                }
            }

            $this->info("🎉 Enhancement completed!");
            $this->info("📊 Stats: Total businesses processed={$businesses->count()}, Enhancement jobs queued={$totalEnhanced}");

        } catch (\Exception $e) {
            $this->error("❌ Error during enhancement: " . $e->getMessage());
            Log::error('Business enhancement error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }


    private function getBusinessesToEnhance(string $priority, int $batchSize): \Illuminate\Database\Eloquent\Collection
    {
        $query = Business::query();

        // Focus on businesses missing the priority field
        switch ($priority) {
            case 'email':
                $query->where(function ($q) {
                    $q->whereNull('email')->orWhere('email', '');
                });
                break;
            case 'coordinates':
                $query->where(function ($q) {
                    $q->whereNull('lat')->orWhereNull('lng')
                      ->orWhere('lat', '')->orWhere('lng', '');
                });
                break;
            case 'address':
                $query->where(function ($q) {
                    $q->whereNull('address')->orWhere('address', '')
                      ->orWhereRaw('address NOT REGEXP "[0-9]{5}"'); // Missing zip code
                });
                break;
            default: // 'all'
                $query->withIncompleteData();
        }

        return $query->limit($batchSize)->get();
    }


    private function getPriorityFields(array $missingFields, string $priority): array
    {
        $priorityMap = [
            'email' => ['email'],
            'coordinates' => ['lat', 'lng'],
            'address' => ['address'],
        ];

        if (isset($priorityMap[$priority])) {
            return array_intersect($missingFields, $priorityMap[$priority]);
        }

        return $missingFields; // Return all missing fields if no specific priority
    }


}
