<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Business;
use App\Jobs\EnhanceBusinessDataJob;
use App\Services\BusinessDataCompletenessService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class EnhanceBusinessesSequentially extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'businesses:enhance-sequential 
                            {--batch-size=5 : Number of businesses to process per batch}
                            {--delay=30 : Delay in seconds between batches}
                            {--max-runtime=300 : Maximum runtime in seconds (5 minutes default)}
                            {--priority=email : Priority field to focus on (email, coordinates, address)}
                            {--dry-run : Show what would be enhanced without actually doing it}';

    /**
     * The console command description.
     */
    protected $description = 'Safely enhance businesses with missing critical fields in small batches with delays';

    /**
     * Cache key for tracking progress
     */
    private const CACHE_KEY = 'business_enhancement_progress';
    
    /**
     * Maximum concurrent enhancement jobs allowed
     */
    private const MAX_CONCURRENT_JOBS = 3;

    /**
     * Execute the console command.
     */
    public function handle(BusinessDataCompletenessService $completenessService)
    {
        $startTime = now();
        $maxRuntime = $this->option('max-runtime');
        $batchSize = $this->option('batch-size');
        $delay = $this->option('delay');
        $priority = $this->option('priority');
        $isDryRun = $this->option('dry-run');

        $this->info("🚀 Starting sequential business enhancement");
        $this->info("📊 Settings: Batch={$batchSize}, Delay={$delay}s, Max Runtime={$maxRuntime}s, Priority={$priority}");
        
        if ($isDryRun) {
            $this->warn("🔍 DRY RUN MODE - No actual changes will be made");
        }

        // Check if another enhancement process is running
        if ($this->isEnhancementRunning()) {
            $this->warn("⚠️  Another enhancement process is already running. Exiting to prevent conflicts.");
            return 1;
        }

        // Mark enhancement as running
        $this->markEnhancementRunning();

        try {
            $totalProcessed = 0;
            $totalEnhanced = 0;
            $lastProcessedId = $this->getLastProcessedId();

            $this->info("📍 Resuming from Business ID: {$lastProcessedId}");

            while (now()->diffInSeconds($startTime) < $maxRuntime) {
                // Check system load before processing
                if ($this->isSystemUnderLoad()) {
                    $this->warn("⚠️  System under load. Waiting before next batch...");
                    sleep($delay * 2); // Double the delay when system is busy
                    continue;
                }

                // Get next batch of businesses to process
                $businesses = $this->getNextBatch($lastProcessedId, $batchSize, $priority);

                if ($businesses->isEmpty()) {
                    $this->info("✅ No more businesses to process. Resetting to beginning.");
                    $lastProcessedId = 0;
                    $this->saveProgress($lastProcessedId);
                    continue;
                }

                $this->line("🔄 Processing batch of {$businesses->count()} businesses...");

                foreach ($businesses as $business) {
                    $missingFields = $completenessService->getMissingCriticalFields($business);
                    
                    if (!empty($missingFields)) {
                        $priorityFields = $this->getPriorityFields($missingFields, $priority);
                        
                        if (!empty($priorityFields)) {
                            if ($isDryRun) {
                                $this->line("  🔍 Would enhance: {$business->name} (ID: {$business->id}) - Fields: " . implode(', ', $priorityFields));
                            } else {
                                // Dispatch with delay to prevent queue flooding
                                EnhanceBusinessDataJob::dispatch($business, $priorityFields)
                                    ->delay(now()->addSeconds(rand(10, 60))); // Random delay 10-60 seconds
                                
                                $this->line("  ✨ Queued: {$business->name} (ID: {$business->id}) - Fields: " . implode(', ', $priorityFields));
                                $totalEnhanced++;
                            }
                        }
                    }

                    $lastProcessedId = $business->id;
                    $totalProcessed++;
                }

                // Save progress
                $this->saveProgress($lastProcessedId);

                // Wait before next batch to prevent overwhelming the system
                if ($delay > 0) {
                    $this->line("⏳ Waiting {$delay} seconds before next batch...");
                    sleep($delay);
                }

                // Check if we should stop due to too many queued jobs
                if ($this->getTotalQueuedJobs() > self::MAX_CONCURRENT_JOBS) {
                    $this->warn("⚠️  Too many enhancement jobs in queue. Pausing...");
                    sleep($delay * 3);
                }
            }

            $runtime = now()->diffInSeconds($startTime);
            $this->info("🎉 Enhancement session completed!");
            $this->info("📊 Stats: Processed={$totalProcessed}, Enhanced={$totalEnhanced}, Runtime={$runtime}s");

        } catch (\Exception $e) {
            $this->error("❌ Error during enhancement: " . $e->getMessage());
            Log::error('Business enhancement error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        } finally {
            // Always clean up
            $this->markEnhancementStopped();
        }

        return 0;
    }

    /**
     * Get next batch of businesses to process
     */
    private function getNextBatch(int $lastId, int $batchSize, string $priority): \Illuminate\Database\Eloquent\Collection
    {
        $query = Business::where('id', '>', $lastId)
            ->orderBy('id');

        // Focus on businesses missing the priority field
        switch ($priority) {
            case 'email':
                $query->where(function ($q) {
                    $q->whereNull('email')->orWhere('email', '');
                });
                break;
            case 'coordinates':
                $query->where(function ($q) {
                    $q->whereNull('lat')->orWhereNull('lng')
                      ->orWhere('lat', '')->orWhere('lng', '');
                });
                break;
            case 'address':
                $query->where(function ($q) {
                    $q->whereNull('address')->orWhere('address', '')
                      ->orWhereRaw('address NOT REGEXP "[0-9]{5}"'); // Missing zip code
                });
                break;
            default:
                // Get any incomplete businesses
                $query->withIncompleteData();
        }

        return $query->limit($batchSize)->get();
    }

    /**
     * Get priority fields based on focus
     */
    private function getPriorityFields(array $missingFields, string $priority): array
    {
        $priorityMap = [
            'email' => ['email'],
            'coordinates' => ['lat', 'lng'],
            'address' => ['address'],
        ];

        if (isset($priorityMap[$priority])) {
            return array_intersect($missingFields, $priorityMap[$priority]);
        }

        return $missingFields; // Return all missing fields if no specific priority
    }

    /**
     * Check if system is under load
     */
    private function isSystemUnderLoad(): bool
    {
        // Check database connections
        $activeConnections = DB::select("SHOW STATUS LIKE 'Threads_connected'")[0]->Value ?? 0;
        
        // Check queue size
        $queueSize = $this->getTotalQueuedJobs();
        
        // Simple load check - adjust thresholds based on your system
        return $activeConnections > 50 || $queueSize > 20;
    }

    /**
     * Get total queued enhancement jobs
     */
    private function getTotalQueuedJobs(): int
    {
        try {
            // This depends on your queue driver
            // For database queue:
            return DB::table('jobs')
                ->where('payload', 'like', '%EnhanceBusinessDataJob%')
                ->count();
        } catch (\Exception $e) {
            return 0; // Fallback if we can't check queue
        }
    }

    /**
     * Check if enhancement is already running
     */
    private function isEnhancementRunning(): bool
    {
        return Cache::has(self::CACHE_KEY . '_running');
    }

    /**
     * Mark enhancement as running
     */
    private function markEnhancementRunning(): void
    {
        Cache::put(self::CACHE_KEY . '_running', true, now()->addMinutes(30));
    }

    /**
     * Mark enhancement as stopped
     */
    private function markEnhancementStopped(): void
    {
        Cache::forget(self::CACHE_KEY . '_running');
    }

    /**
     * Get last processed business ID
     */
    private function getLastProcessedId(): int
    {
        return Cache::get(self::CACHE_KEY . '_last_id', 0);
    }

    /**
     * Save progress
     */
    private function saveProgress(int $lastId): void
    {
        Cache::put(self::CACHE_KEY . '_last_id', $lastId, now()->addDays(7));
    }
}
