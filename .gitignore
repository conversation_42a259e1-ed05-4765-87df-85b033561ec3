/vendor/
node_modules/
npm-debug.log
yarn-error.log

# Laravel 4 specific
bootstrap/compiled.php
app/storage/

# <PERSON>vel 5 & <PERSON><PERSON> specific
public/storage
public/hot

# <PERSON>vel 5 & <PERSON><PERSON> specific with changed public path
public_html/storage
public_html/hot

storage/*.key
.env
Homestead.yaml
Homestead.json
/.vagrant
.phpunit.result.cache

/public/build
/storage/pail
.env.backup
.env.production
.phpactor.json
auth.json

# Jetbrains
.idea/

# Framework
storage/framework/

.trae

.roo
.taskmaster
.roomodes
.windsurfrules
prd.txt
.cursor

# Added by Task Master AI
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/ 
.env.example

docs/