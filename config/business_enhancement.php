<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Business Enhancement Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the automatic business data enhancement system.
    | These settings control how the system safely enhances business data
    | in production without impacting main application performance.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Sequential Enhancement Settings
    |--------------------------------------------------------------------------
    */
    'sequential' => [
        // Default batch size for processing businesses
        'batch_size' => env('BUSINESS_ENHANCEMENT_BATCH_SIZE', 5),
        
        // Delay between batches in seconds
        'delay_between_batches' => env('BUSINESS_ENHANCEMENT_DELAY', 30),
        
        // Maximum runtime for a single enhancement session (seconds)
        'max_runtime' => env('BUSINESS_ENHANCEMENT_MAX_RUNTIME', 300), // 5 minutes
        
        // Maximum concurrent enhancement jobs in queue
        'max_concurrent_jobs' => env('BUSINESS_ENHANCEMENT_MAX_JOBS', 3),
        
        // Random delay range for job dispatch (min, max seconds)
        'job_delay_range' => [10, 60],
    ],

    /*
    |--------------------------------------------------------------------------
    | System Load Protection
    |--------------------------------------------------------------------------
    */
    'load_protection' => [
        // Maximum database connections before pausing
        'max_db_connections' => env('BUSINESS_ENHANCEMENT_MAX_DB_CONN', 50),
        
        // Maximum queue size before pausing
        'max_queue_size' => env('BUSINESS_ENHANCEMENT_MAX_QUEUE', 20),
        
        // Multiplier for delay when system is under load
        'load_delay_multiplier' => 2,
    ],

    /*
    |--------------------------------------------------------------------------
    | Priority Field Configuration
    |--------------------------------------------------------------------------
    */
    'priorities' => [
        'email' => [
            'description' => 'Focus on businesses missing email addresses',
            'fields' => ['email'],
            'weight' => 10, // Highest priority
        ],
        'coordinates' => [
            'description' => 'Focus on businesses missing lat/lng coordinates',
            'fields' => ['lat', 'lng'],
            'weight' => 8,
        ],
        'address' => [
            'description' => 'Focus on businesses with incomplete addresses',
            'fields' => ['address'],
            'weight' => 6,
        ],
        'contact' => [
            'description' => 'Focus on businesses missing contact information',
            'fields' => ['phone', 'website'],
            'weight' => 4,
        ],
        'all' => [
            'description' => 'Process all missing critical fields',
            'fields' => ['email', 'lat', 'lng', 'address', 'phone', 'website'],
            'weight' => 1,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Scheduling Configuration
    |--------------------------------------------------------------------------
    */
    'schedule' => [
        // Enable automatic scheduling
        'enabled' => env('BUSINESS_ENHANCEMENT_SCHEDULE_ENABLED', true),
        
        // Cron expression for when to run enhancement
        // Default: Every 30 minutes during business hours (9 AM - 6 PM)
        'cron' => env('BUSINESS_ENHANCEMENT_CRON', '*/30 9-18 * * *'),
        
        // Alternative: Run every X minutes
        'frequency_minutes' => env('BUSINESS_ENHANCEMENT_FREQUENCY', null),
        
        // Prevent overlapping runs
        'without_overlapping' => true,
        
        // Maximum execution time for scheduled runs
        'timeout' => 600, // 10 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Logging
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        // Log enhancement activities
        'log_enabled' => env('BUSINESS_ENHANCEMENT_LOG', true),
        
        // Log level for enhancement activities
        'log_level' => env('BUSINESS_ENHANCEMENT_LOG_LEVEL', 'info'),
        
        // Send notifications for enhancement completion
        'notifications_enabled' => env('BUSINESS_ENHANCEMENT_NOTIFICATIONS', false),
        
        // Email addresses to notify (comma-separated)
        'notification_emails' => env('BUSINESS_ENHANCEMENT_NOTIFICATION_EMAILS', ''),
        
        // Slack webhook for notifications
        'slack_webhook' => env('BUSINESS_ENHANCEMENT_SLACK_WEBHOOK', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | Safety Limits
    |--------------------------------------------------------------------------
    */
    'safety' => [
        // Maximum businesses to enhance per day
        'daily_limit' => env('BUSINESS_ENHANCEMENT_DAILY_LIMIT', 1000),
        
        // Minimum delay between enhancement attempts for same business (hours)
        'business_cooldown_hours' => env('BUSINESS_ENHANCEMENT_COOLDOWN', 24),
        
        // Enable circuit breaker for error protection
        'circuit_breaker_enabled' => true,
        
        // Maximum failures before circuit breaker trips
        'max_failures' => 5,
        
        // Circuit breaker reset time (minutes)
        'circuit_breaker_reset' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    */
    'queue' => [
        // Dedicated queue name for enhancement jobs
        'name' => env('BUSINESS_ENHANCEMENT_QUEUE', 'business-enhancement'),
        
        // Queue connection to use
        'connection' => env('BUSINESS_ENHANCEMENT_QUEUE_CONNECTION', 'database'),
        
        // Job timeout (seconds)
        'timeout' => env('BUSINESS_ENHANCEMENT_JOB_TIMEOUT', 300),
        
        // Number of job retries
        'retries' => env('BUSINESS_ENHANCEMENT_JOB_RETRIES', 3),
        
        // Backoff strategy for retries (seconds)
        'backoff' => [30, 60, 120],
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Quality Thresholds
    |--------------------------------------------------------------------------
    */
    'quality' => [
        // Minimum completeness score to consider business "complete"
        'min_completeness_score' => 70,
        
        // Fields that are absolutely required
        'required_fields' => ['email', 'name'],
        
        // Fields that are highly recommended
        'recommended_fields' => ['lat', 'lng', 'address', 'phone'],
        
        // Fields that are nice to have
        'optional_fields' => ['website', 'category'],
    ],
];
