<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\JobBookingController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::group(['middleware' => ['localization']], function () {

    // Webhook endpoints for job notifications
    Route::prefix('webhooks')->group(function () {
        Route::post('/job-posted', 'App\Http\Controllers\API\WebhookController@handleJobPostedEvent');
        Route::get('/health', 'App\Http\Controllers\API\WebhookController@healthCheck');
    });
    
    Route::post('/login', 'App\Http\Controllers\API\AuthController@login');
    Route::post('/social/login', 'App\Http\Controllers\API\AuthController@socialLogin');
    Route::get('/logout', 'App\Http\Controllers\API\AuthController@logout');
    Route::post('/forgot-password', 'App\Http\Controllers\API\AuthController@forgotPassword');
    Route::post('/verifyOtp', 'App\Http\Controllers\API\AuthController@verifyOtp');
    Route::post('/sendOtp', 'App\Http\Controllers\API\AuthController@sendOtp');
    Route::post('/verifySendOtp', 'App\Http\Controllers\API\AuthController@verifySendOtp');
    Route::post('/update-password', 'App\Http\Controllers\API\AuthController@updatePassword');
    Route::post('/register', 'App\Http\Controllers\API\AuthController@register');

    // Zones
    Route::apiResource('zone', 'App\Http\Controllers\API\ZoneController', ['except' => ['show']]);
    Route::get('zone-by-point', 'App\Http\Controllers\API\ZoneController@getZoneIds')->name('get.zoneId');

    // Countries & States
    Route::apiResource('state', 'App\Http\Controllers\API\StateController');
    Route::post('add-state', 'App\Http\Controllers\API\StateController@store');
    Route::apiResource('country', 'App\Http\Controllers\API\CountryController');

    // Settings & Options
    Route::get('settings', 'App\Http\Controllers\API\SettingController@frontSettings');

    // Payment Methods
    Route::get('payment-methods', 'App\Http\Controllers\API\PaymentMethodController@index');

    // Role With Permissions
    Route::apiResource('role', 'App\Http\Controllers\API\RoleController');

    // Users
    Route::get('user', 'App\Http\Controllers\API\UserController@index');
    Route::get('user/count-active', 'App\Http\Controllers\API\UserController@countActive');

    //Self
    Route::get('self', 'App\Http\Controllers\API\AccountController@self');
    Route::put('zone-update','App\Http\Controllers\API\AccountController@updateUserZone');
    
    // Providers
    Route::get('provider', 'App\Http\Controllers\API\ProviderController@index');
    Route::get('provider/{id}', 'App\Http\Controllers\API\ProviderController@show');
    Route::get('providerServices', 'App\Http\Controllers\API\ProviderController@getProviderServices');
    Route::post('provider-zone/update', 'App\Http\Controllers\API\ProviderController@updateProviderZones');

    // Banner
    Route::get('banner', 'App\Http\Controllers\API\BannerController@index');
    Route::get('banner/{id}', 'App\Http\Controllers\API\BannerController@show');

    // Servicemans
    Route::apiResource('serviceman', 'App\Http\Controllers\API\ServicemanController', [
        'only' => ['index', 'show'],
    ]);

    // Service
    Route::get('service', 'App\Http\Controllers\API\ServiceController@index');
    Route::get('featuredServices', 'App\Http\Controllers\API\ServiceController@isFeatured');
    Route::get('servicePackages', 'App\Http\Controllers\API\ServiceController@servicePackages');
    Route::get('servicePackages/{id}', 'App\Http\Controllers\API\ServiceController@servicePackages');

    // Categories
    Route::get('category', 'App\Http\Controllers\API\CategoryController@index');
    Route::get('categoryList', 'App\Http\Controllers\API\CategoryController@getAllCategories');
    Route::get('category/{id}', 'App\Http\Controllers\API\CategoryController@show');

    // Currency
    Route::get('currency', 'App\Http\Controllers\API\CurrencyController@index');

    // Blog
    Route::get('blog', 'App\Http\Controllers\API\BlogController@index');

    // Blog-Category
    Route::get('blog-category', 'App\Http\Controllers\API\BlogController@index');

    // Pages
    Route::get('page', 'App\Http\Controllers\API\PageController@index');

    //Service FAQ's
    Route::get('service-faqs', 'App\Http\Controllers\API\ServiceController@serviceFAQS');

    Route::get('/providers/highest-ratings', 'App\Http\Controllers\API\ProviderController@getUsersWithHighestRatings');

    // Order Status
    Route::apiResource('bookingStatus', 'App\Http\Controllers\API\BookingStatusController', [
        'only' => ['index', 'show'],
    ]);

    // Job Booking
    Route::post('job-bookings', 'App\Http\Controllers\API\JobBookingController@store');

    Route::get('job-bookings/my-job-bookings', [\App\Http\Controllers\API\JobBookingController::class, 'getMyJobBookings']);

    // Job Booking (public routes)
    Route::get('job-bookings', 'App\Http\Controllers\API\JobBookingController@index');
    Route::get('job-bookings/external', 'App\Http\Controllers\API\JobBookingController@getExternalJobs');
    Route::get('job-bookings/{jobUuid}/providers', 'App\Http\Controllers\API\JobBookingController@getProviders');
    Route::post('job-bookings/{jobUuid}/send', 'App\Http\Controllers\API\JobBookingController@sendToProviders');

    // Upload Assets
    Route::prefix('assets')->group(function () {
        Route::post('upload', 'App\Http\Controllers\API\AssetController@upload');
        Route::delete('{uuid}', 'App\Http\Controllers\API\AssetController@delete');
    });
    /*------------------------------------ Provider API's ------------------------------------
    --------------------- */

    // Auth API's
    Route::post('/provider-register', 'App\Http\Controllers\API\AuthController@registerProvider');

    //Document
    Route::apiResource('document', 'App\Http\Controllers\API\DocumentController', [
        'only' => ['index', 'show'],
    ]);

    //Tax
    Route::apiResource('tax', 'App\Http\Controllers\API\TaxController', [
        'only' => ['index', 'show'],
    ]);

    //Language
    Route::apiResource('language', 'App\Http\Controllers\API\LanguageController', [
        'only' => ['index', 'show'],
    ]);

    // System Language
    Route::apiResource('systemLang', 'App\Http\Controllers\API\SystemLangController', [
        'only' => ['index'],
    ]);

    Route::get('systemLang/translate/{lang?}', 'App\Http\Controllers\API\SystemLangController@getTranslate');

    // Business
    Route::get('businesses', 'App\Http\Controllers\API\BusinessController@index');
    Route::get('businesses/categories', 'App\Http\Controllers\API\BusinessController@getCategories');
    Route::get('businesses/{businessUuid}', 'App\Http\Controllers\API\BusinessController@show');
    Route::post('businesses/import', 'App\Http\Controllers\API\BusinessController@import');

    Route::post('businesses', [\App\Http\Controllers\API\BusinessController::class, 'store']);
    Route::put('businesses/{businessUuid}', [\App\Http\Controllers\API\BusinessController::class, 'update']);
    Route::delete('businesses/{businessUuid}', [\App\Http\Controllers\API\BusinessController::class, 'destroy']);

    // Business Enhancement API
    Route::prefix('business-enhancement')->group(function () {
        Route::get('status', [\App\Http\Controllers\API\BusinessEnhancementController::class, 'status']);
        Route::post('enhance', [\App\Http\Controllers\API\BusinessEnhancementController::class, 'enhance']);
    });

    // Admin endpoints (protected by authentication)
    Route::middleware(['auth:api'])->prefix('admin')->group(function () {
        Route::get('certificates/reviews', [\App\Http\Controllers\API\UserController::class, 'listCertificatesReviews']);
        Route::post('certificates/reviews/{userId}/approve', [\App\Http\Controllers\API\UserController::class, 'approveCertificatesReview']);
        Route::post('certificates/reviews/{userId}/reject', [\App\Http\Controllers\API\UserController::class, 'rejectCertificatesReview']);
    
        // Scraping Jobs
        Route::apiResource('scraping-jobs', \App\Http\Controllers\API\ScrapingJobController::class)->only(['index', 'show']);
        Route::post('scraping-jobs/{scrapingJob}/cancel', [\App\Http\Controllers\API\ScrapingJobController::class, 'cancel']);
        Route::post('scraping-jobs/{scrapingJob}/retry', [\App\Http\Controllers\API\ScrapingJobController::class, 'retry']);
        Route::get('scraping-jobs/{scrapingJob}/status', [\App\Http\Controllers\API\ScrapingJobController::class, 'status']);
    });

    // Commented out: NotificationCampaignController admin API routes (controller missing)
    // Route::middleware(['auth:api'])->prefix('admin/notification-campaigns')->group(function () {
    //     Route::get('/', 'App\\Http\\Controllers\\Admin\\NotificationCampaignController@index')->name('api.admin.notification-campaigns.index');
    //     Route::get('/{campaignId}', 'App\\Http\\Controllers\\Admin\\NotificationCampaignController@show')->name('api.admin.notification-campaigns.show');
    //     Route::post('/{campaignId}/approve', 'App\\Http\\Controllers\\Admin\\NotificationCampaignController@approve')->name('api.admin.notification-campaigns.approve');
    //     Route::post('/{campaignId}/reject', 'App\\Http\\Controllers\\Admin\\NotificationCampaignController@reject')->name('api.admin.notification-campaigns.reject');
    // });

    // Job Notification Campaign APIs
    Route::prefix('job-notification-campaigns')->group(function () {
        Route::get('/', [\App\Http\Controllers\API\JobNotificationCampaignController::class, 'index']);
        Route::get('/{id}', [\App\Http\Controllers\API\JobNotificationCampaignController::class, 'show']);
        Route::get('/{id}/recipients', [\App\Http\Controllers\API\JobNotificationRecipientController::class, 'index']);

        // Token-based approval/rejection routes (public - token protected)
        Route::get('/{id}/token/{token}', [\App\Http\Controllers\API\JobNotificationCampaignController::class, 'showWithToken']);
        Route::post('/{id}/approve/{token}', [\App\Http\Controllers\API\JobNotificationCampaignController::class, 'approve']);
        Route::post('/{id}/reject/{token}', [\App\Http\Controllers\API\JobNotificationCampaignController::class, 'reject']);
    });

    Route::prefix('job-notification-recipients')->group(function () {
        Route::get('/{id}', [\App\Http\Controllers\API\JobNotificationRecipientController::class, 'show']);
        Route::get('/{id}/email', [\App\Http\Controllers\API\JobNotificationRecipientController::class, 'email']);
    });

    /*------------------------------------ End Provider API's ------------------------------------------------------ */

    Route::middleware('auth:api')->group(function () {
        Route::get('profile', [\App\Http\Controllers\API\AccountController::class, 'getProfile']);
        Route::put('profile', [\App\Http\Controllers\API\AccountController::class, 'updateProfile']);
        Route::post('addserviceProofs', 'App\Http\Controllers\API\BookingController@addserviceProofs');
        Route::post('updateserviceProofs', 'App\Http\Controllers\API\BookingController@updateserviceProofs');
        Route::get('statistics/count', 'App\Http\Controllers\API\HomeController@index');
        Route::get('home/chart', 'App\Http\Controllers\API\HomeController@chart');
        Route::get('home/get-top-earning-categories', 'App\Http\Controllers\API\HomeController@getTopCategoryEarnings');
        Route::put('updateProfile', 'App\Http\Controllers\API\AccountController@updateProfile');
        Route::post('store/notification', 'App\Http\Controllers\API\NotificationController@store');
        Route::post('dummy/notification', 'App\Http\Controllers\API\NotificationController@dummyNotification');
        Route::post('update-company-details', 'App\Http\Controllers\API\ProviderController@updateCompanyDetails');

        Route::patch('user/certificates', [\App\Http\Controllers\API\UserController::class, 'updateCertificates']);
        Route::post('user/certificates/request-review', [\App\Http\Controllers\API\UserController::class, 'requestCertificatesReview']);

        // Delete Account
        Route::get('deleteAccount', 'App\Http\Controllers\API\AccountController@deleteAccount')->name('deleteAccount');

        Route::apiResource('user', \App\Http\Controllers\API\UserController::class);

        // category-commission
        Route::get('category-commission', 'App\Http\Controllers\API\CategoryController@getCategoryCommission');

        //Provider
        Route::apiResource('provider', 'App\Http\Controllers\API\ProviderController', [
            'only' => ['store', 'update', 'destroy'],
        ]);

        //Service
        Route::apiResource('service', 'App\Http\Controllers\API\ServiceController', [
            'only' => ['store', 'update', 'destroy'],
        ]);
        Route::post('addServiceAddress/{id}', 'App\Http\Controllers\API\ServiceController@storeServiceAddresses');
        Route::delete('deleteServiceAddress/{id}/{address_id}', 'App\Http\Controllers\API\ServiceController@deleteServiceAddresses');

        //Service Package
        Route::apiResource('service-package', 'App\Http\Controllers\API\ServicePackageController', [
            'only' => ['store', 'update', 'destroy'],
        ]);

        //Serviceman
        Route::apiResource('serviceman', 'App\Http\Controllers\API\ServicemanController', [
            'only' => ['store', 'update', 'destroy'],
        ]);

        //Bank Detail
        Route::apiResource('bankDetail', 'App\Http\Controllers\API\BankDetailController')->except(['update']);
        Route::put('bankDetail/{user_id}', 'App\Http\Controllers\API\BankDetailController@update')->middleware('can:backend.bank_detail.edit');


        Route::get('company/{companyId}/addresses', 'App\Http\Controllers\API\CompanyController@getCompanyAddresses');
        Route::get('provider-time-slot/{provider_id}', 'App\Http\Controllers\API\ProviderController@providerTimeSlot');
        Route::post('provider-time-slot', 'App\Http\Controllers\API\ProviderController@storeProviderTimeSlot');
        Route::put('update-provider-time-slot', 'App\Http\Controllers\API\ProviderController@updateProviderTimeSlot');

        //Rate App
        Route::post('rate-app', 'App\Http\Controllers\API\RateAppController@store');

        //Address Status
        Route::post('changeAddressStatus/{id}', 'App\Http\Controllers\API\AddressController@changeAddressStatus');

        Route::apiResource('banner', 'App\Http\Controllers\API\BannerController', [
            'only' => ['store', 'update', 'destroy'],
        ]);

        Route::apiResource('category', 'App\Http\Controllers\API\CategoryController', [
            'only' => ['store', 'update', 'destroy'],
        ]);

        Route::apiResource('currency', 'App\Http\Controllers\API\CurrencyController', [
            'only' => ['store', 'update', 'destroy'],
        ]);

        //Booking
        Route::apiResource('booking', 'App\Http\Controllers\API\BookingController');
        Route::get('isValidTimeSlot', 'App\Http\Controllers\API\ProviderController@isValidTimeSlot');
        Route::post('checkout', 'App\Http\Controllers\API\CheckoutController@verifyCheckout');
        Route::post('booking/assign', 'App\Http\Controllers\API\BookingController@assign');
        Route::post('booking/add-extra-charges', 'App\Http\Controllers\API\BookingController@addExtraCharges');
        Route::post('booking/payment', 'App\Http\Controllers\API\BookingController@payment');

        // Job Booking authenticated routes
        Route::get('job-bookings/{jobBooking}', 'App\Http\Controllers\API\JobBookingController@show');
        Route::put('job-bookings/{jobBooking}', 'App\Http\Controllers\API\JobBookingController@update');
        Route::delete('job-bookings/{jobBooking}', 'App\Http\Controllers\API\JobBookingController@destroy');
        Route::post('job-bookings/{jobBooking}/accept-bid', [JobBookingController::class, 'acceptBid'])->name('job-bookings.accept-bid');
        Route::post('job-bookings/{jobBooking}/reject-bid', [JobBookingController::class, 'rejectBid'])->name('job-bookings.reject-bid');

        // Customer Job Booking routes
        Route::prefix('customer')->group(function () {
            Route::get('job-bookings', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'index']);
            Route::post('job-bookings', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'store']);
            Route::get('job-bookings/{jobUuid}', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'show']);
            Route::put('job-bookings/{jobUuid}', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'update']);
            Route::patch('job-bookings/{jobUuid}', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'update']);
            Route::post('job-bookings/{jobUuid}/cancel', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'cancel']);
            Route::get('job-bookings/{jobUuid}/bids', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'getBids']);
            Route::post('job-bookings/{jobUuid}/bids/{bidId}/accept', [\App\Http\Controllers\API\Customer\JobBookingController::class, 'acceptBid']);
        });

        // Provider Job Booking and Bid routes
        Route::prefix('provider')->group(function () {
            // Job booking details with bids for providers
            Route::get('job-bookings/{jobId}', [\App\Http\Controllers\API\Provider\JobBidController::class, 'getJobBookingWithBids']);

            // Bid management
            Route::get('bids', [\App\Http\Controllers\API\Provider\JobBidController::class, 'index']);
            Route::post('job-bookings/{job_booking}/bids', [\App\Http\Controllers\API\Provider\JobBidController::class, 'store']);
            Route::get('bids/{bid}', [\App\Http\Controllers\API\Provider\JobBidController::class, 'show']);
            Route::put('bids/{bid}', [\App\Http\Controllers\API\Provider\JobBidController::class, 'update']);
            Route::patch('bids/{bid}', [\App\Http\Controllers\API\Provider\JobBidController::class, 'update']);
            Route::delete('bids/{bid}', [\App\Http\Controllers\API\Provider\JobBidController::class, 'destroy']);

            // Job management (actual jobs from accepted bids)
            Route::get('jobs', [\App\Http\Controllers\API\Provider\JobController::class, 'index']);
            Route::get('jobs/{job}', [\App\Http\Controllers\API\Provider\JobController::class, 'show']);
            Route::patch('jobs/{job}/status', [\App\Http\Controllers\API\Provider\JobController::class, 'updateStatus']);
            Route::post('jobs/{job}/start', [\App\Http\Controllers\API\Provider\JobController::class, 'start']);
            Route::post('jobs/{job}/complete', [\App\Http\Controllers\API\Provider\JobController::class, 'complete']);
        });

        // Job Bid routes
        Route::get('job-bookings/{job_booking}/bids', [\App\Http\Controllers\API\JobBidController::class, 'index']);
        Route::post('job-bookings/{job_booking}/bids', [\App\Http\Controllers\API\JobBidController::class, 'store']);
        Route::get('job-bids/{bid}', [\App\Http\Controllers\API\JobBidController::class, 'show']);
        Route::put('job-bids/{bid}', [\App\Http\Controllers\API\JobBidController::class, 'update']);
        Route::patch('job-bids/{bid}', [\App\Http\Controllers\API\JobBidController::class, 'update']);
        Route::delete('job-bids/{bid}', [\App\Http\Controllers\API\JobBidController::class, 'destroy']);

        //Payment
        Route::get('verifyPayment', 'App\Http\Controllers\API\BookingController@verifyPayment');
        Route::post('re-payment', 'App\Http\Controllers\API\BookingController@rePayment');

        // Booking Status
        Route::apiResource('bookingStatus', 'App\Http\Controllers\API\BookingStatusController', [
            'only' => ['store', 'update', 'destroy'],
        ]);
        Route::post('bookingStatus/deleteAll', 'App\Http\Controllers\API\BookingStatusController@deleteAll');
        Route::put('bookingStatus/{id}/{status}', 'App\Http\Controllers\API\BookingStatusController@status');

        // Fabourite-List
        Route::apiResource('favourite-list', 'App\Http\Controllers\API\FavouriteListController');
        Route::delete('/favorite-list/{type}/{id}', 'App\Http\Controllers\API\FavouriteListController@destroy');

        Route::put('address/isPrimary/{id}', 'App\Http\Controllers\API\AddressController@isPrimary');

        Route::apiResource('address', 'App\Http\Controllers\API\AddressController', [
            'only' => ['index', 'store', 'update', 'destroy'],
        ]);

        // Notifications
        Route::get('notifications', 'App\Http\Controllers\API\NotificationController@index');
        Route::get('clear-notifications', 'App\Http\Controllers\API\NotificationController@clearNotifications');
        Route::put('notifications/markAsRead', 'App\Http\Controllers\API\NotificationController@markAsRead');
        Route::delete('notifications/{id}', 'App\Http\Controllers\API\NotificationController@destroy');

        // Reviews
        Route::apiResource('review', 'App\Http\Controllers\API\ReviewController');
        Route::post('review/deleteAll', 'App\Http\Controllers\API\ReviewController@deleteAll');

        // Wallets
        Route::get('wallet/consumer', 'App\Http\Controllers\API\WalletController@index');
        Route::post('credit/wallet', 'App\Http\Controllers\API\WalletController@credit');
        Route::post('debit/wallet', 'App\Http\Controllers\API\WalletController@debit');
        Route::post('provider/top-up', 'App\Http\Controllers\API\ProviderWalletController@topUp');
        Route::post('provider/withdraw-request', 'App\Http\Controllers\API\ProviderWalletController@withdrawRequest');
        Route::post('consumer/top-up', 'App\Http\Controllers\API\WalletController@topUp');

        // Provider Wallets
        Route::get('wallet/provider', 'App\Http\Controllers\API\ProviderWalletController@index')->middleware('can:backend.provider_wallet.index');
        Route::post('debit/providerWallet', 'App\Http\Controllers\API\ProviderWalletController@debit')->middleware('can:backend.provider_wallet.debit');
        Route::post('credit/providerWallet', 'App\Http\Controllers\API\ProviderWalletController@credit')->middleware('can:backend.provider_wallet.credit');

        // Serviceman Wallets
        Route::get('wallet/serviceman', 'App\Http\Controllers\API\ServicemanWalletController@index')->middleware('can:backend.serviceman_wallet.index');
        Route::post('serviceman/top-up', 'App\Http\Controllers\API\ServicemanWalletController@topUp')->middleware('can:backend.serviceman_withdraw_request.create');

        // Commission Histories
        Route::apiResource('commissionHistory', 'App\Http\Controllers\API\CommissionHistoryController', [
            'only' => ['index', 'show'],
        ]);

        // Delete Account
        Route::get('deleteAccount', 'App\Http\Controllers\API\AccountController@deleteAccount')->name('deleteAccount');

        // Account
        Route::put('update-password', 'App\Http\Controllers\API\AccountController@updatePassword');
        Route::put('account/change-password', [\App\Http\Controllers\API\AccountController::class, 'changePassword']);

        //verify serviceman
        Route::get('verify-user/{userId}', 'App\Http\Controllers\API\DocumentController@verifyUserDocument');
        Route::get('userDocuments/{userId}', 'App\Http\Controllers\API\DocumentController@getUserDocuments');

        //upload provider document
        Route::post('/upload-provider-document', 'App\Http\Controllers\API\DocumentController@uploadProviderDocument');

        // Bids
        Route::apiResource('bid','App\Http\Controllers\API\BidController',['except' => ['show']]);

        // Provider bid endpoints - REMOVED: This conflicts with the provider prefix group route above
        // Route::get('provider/bids', 'App\Http\Controllers\API\BidController@providerBids');
        
        // Customer bid endpoints
        Route::get('service-requests/{serviceRequestId}/bids', 'App\Http\Controllers\API\BidController@serviceRequestBids');
        Route::patch('bids/{bid}/status', 'App\Http\Controllers\API\BidController@updateStatus');
        
        // Admin bid endpoints
        Route::get('admin/bids', 'App\Http\Controllers\API\BidController@adminBids');
        Route::get('admin/bid-stats', 'App\Http\Controllers\API\BidController@adminBidStats');

        // Service Requests
        Route::apiResource('serviceRequest','App\Http\Controllers\API\ServiceRequestController');

        // User
        // Route::get('user/count-active', 'App\Http\Controllers\API\UserController@countActive');

        // User Addressesták
        Route::get('user-addresses', 'App\Http\Controllers\API\UserAddressController@index');
        Route::get('user-addresses/search', 'App\Http\Controllers\API\UserAddressController@search');

        // Suspend Provider
        Route::put('providers/{id}/suspend', [\App\Http\Controllers\API\UserController::class, 'suspendProvider']);

        // Unsuspend Provider
        Route::put('providers/{id}/unsuspend', [\App\Http\Controllers\API\UserController::class, 'unsuspendProvider']);
    });

    Route::middleware('auth:api')->prefix('chats')->group(function () {
        Route::get('/', [\App\Http\Controllers\API\ChatController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\API\ChatController::class, 'createChat']);
        Route::post('/business', [\App\Http\Controllers\API\ChatController::class, 'createChatByBusiness']);
        Route::post('/group', [\App\Http\Controllers\API\ChatController::class, 'createGroupChat']);
        Route::post('/admin', [\App\Http\Controllers\API\ChatController::class, 'createAdminChat']);
        Route::get('{chat}/messages', [\App\Http\Controllers\API\ChatController::class, 'messages']);
        Route::post('{chat}/messages', [\App\Http\Controllers\API\ChatController::class, 'sendMessage']);
        Route::post('{chat}/read', [\App\Http\Controllers\API\ChatController::class, 'markAsRead']);
        Route::delete('{chat}/messages/{message}', [\App\Http\Controllers\API\ChatController::class, 'deleteMessage']);
    });

    Route::get('roles-public', [\App\Http\Controllers\API\RoleController::class, 'getRolesPublic']);

    Route::post('/auth/google', 'App\Http\Controllers\API\AuthController@googleLogin');
});