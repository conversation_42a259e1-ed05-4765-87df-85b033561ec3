<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * This migration creates the complete scraping job system including:
     * - Scraping jobs table
     * - Database indexes for performance
     * - Updated enum values for job notification campaigns
     */
    public function up(): void
    {
        // Create scraping_jobs table
        Schema::create('scraping_jobs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('job_notification_campaign_id');
            $table->string('location_query');
            $table->string('category_query')->nullable();
            $table->enum('status', ['pending', 'in_progress', 'completed', 'failed'])->default('pending');
            $table->integer('progress')->nullable();
            $table->integer('discovered_businesses_count')->default(0);
            $table->text('message')->nullable();
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('job_notification_campaign_id')
                  ->references('id')
                  ->on('job_notification_campaigns')
                  ->onDelete('cascade');
        });

        // Add indexes to scraping_jobs table for performance
        Schema::table('scraping_jobs', function (Blueprint $table) {
            $table->index('status');
            $table->index('created_at');
            $table->index(['status', 'created_at']);
            $table->index(['job_notification_campaign_id', 'status']);
            $table->index('location_query');
            $table->index('category_query');
        });

        // Add indexes to job_notification_campaigns table for performance
        Schema::table('job_notification_campaigns', function (Blueprint $table) {
            // Get existing indexes to avoid duplicates
            $indexes = collect(Schema::getIndexes('job_notification_campaigns'))->pluck('name');

            // Add indexes for common query patterns (only if they don't exist)
            if (!$indexes->contains('job_notification_campaigns_status_index')) {
                $table->index('status');
            }

            if (!$indexes->contains('job_notification_campaigns_created_at_index')) {
                $table->index('created_at');
            }
            if (!$indexes->contains('job_notification_campaigns_status_created_at_index')) {
                $table->index(['status', 'created_at']);
            }
            if (!$indexes->contains('job_notification_campaigns_job_id_status_index')) {
                $table->index(['job_id', 'status']);
            }
            if (!$indexes->contains('job_notification_campaigns_job_title_index')) {
                $table->index('job_title');
            }

            if (!$indexes->contains('job_notification_campaigns_event_id_index')) {
                $table->index('event_id');
            }
        });

        // Update job_notification_campaigns status enum to include new values
        Schema::table('job_notification_campaigns', function (Blueprint $table) {
            // Change the enum to include the new status values
            $table->enum('status', [
                'pending',
                'approved',
                'rejected',
                'sent',
                'failed',
                'completed',
                'pending_scraping'
            ])->default('pending')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop scraping_jobs table
        Schema::dropIfExists('scraping_jobs');

        // Remove indexes from job_notification_campaigns table
        Schema::table('job_notification_campaigns', function (Blueprint $table) {
            // Only drop indexes that we actually created (not the original ones)
            $indexes = collect(Schema::getIndexes('job_notification_campaigns'))->pluck('name');

            if ($indexes->contains('job_notification_campaigns_status_index')) {
                $table->dropIndex(['status']);
            }

            if ($indexes->contains('job_notification_campaigns_created_at_index')) {
                $table->dropIndex(['created_at']);
            }
            if ($indexes->contains('job_notification_campaigns_status_created_at_index')) {
                $table->dropIndex(['status', 'created_at']);
            }
            if ($indexes->contains('job_notification_campaigns_job_id_status_index')) {
                $table->dropIndex(['job_id', 'status']);
            }
            if ($indexes->contains('job_notification_campaigns_job_title_index')) {
                $table->dropIndex(['job_title']);
            }

            if ($indexes->contains('job_notification_campaigns_event_id_index')) {
                $table->dropIndex(['event_id']);
            }
        });

        // Revert job_notification_campaigns status enum to original values
        Schema::table('job_notification_campaigns', function (Blueprint $table) {
            $table->enum('status', [
                'pending',
                'approved',
                'rejected',
                'sent'
            ])->default('pending')->change();
        });
    }
};
