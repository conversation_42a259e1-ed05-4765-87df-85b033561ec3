<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\JobNotificationCampaign;
use App\Models\ScrapingJob;
use App\Models\Business;
use App\Jobs\ProcessJobNotificationJob;
use App\Jobs\ScrapeBusinessesJob;
use App\Services\ScrapingService;
use App\Services\JobNotificationService;
use App\Services\BusinessDiscoveryService;
use App\Enums\JobNotificationStatusEnum;
use App\Enums\ScrapingJobStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Mail;
use Mockery;

class ScrapingJobFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
        Queue::fake();
    }

    /** @test */
    public function it_creates_scraping_job_when_no_businesses_found()
    {
        // Mock BusinessDiscoveryService to return no businesses
        $this->mock(BusinessDiscoveryService::class, function ($mock) {
            $mock->shouldReceive('findBusinesses')
                 ->once()
                 ->andReturn(collect([]));
        });

        $jobData = [
            'job_id' => 'test-job-123',
            'title' => 'Test Cleaning Job',
            'description' => 'Test job description',
            'zip_code' => '12345',
            'category_id' => 'cleaning',
            'budget' => 100.00,
            'customer_name' => 'John Doe',
            'customer_email' => '<EMAIL>',
        ];

        $job = new ProcessJobNotificationJob($jobData, 'test-event-123');
        $job->handle(
            app(BusinessDiscoveryService::class),
            app(JobNotificationService::class)
        );

        // Assert campaign was created with PENDING_SCRAPING status
        $this->assertDatabaseHas('job_notification_campaigns', [
            'job_id' => 'test-job-123',
            'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
            'business_count' => 0,
        ]);

        // Assert scraping job was created
        $this->assertDatabaseHas('scraping_jobs', [
            'location_query' => '12345',
            'category_query' => 'cleaning',
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        // Assert ScrapeBusinessesJob was dispatched
        Queue::assertPushed(ScrapeBusinessesJob::class);
    }

    /** @test */
    public function it_processes_campaign_after_successful_scraping()
    {
        // Create a campaign and scraping job
        $campaign = JobNotificationCampaign::factory()->create([
            'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
            'business_count' => 0,
        ]);

        $scrapingJob = ScrapingJob::factory()->create([
            'job_notification_campaign_id' => $campaign->id,
            'status' => ScrapingJobStatusEnum::IN_PROGRESS,
        ]);

        // Create some businesses that would be found after scraping
        Business::factory()->count(3)->create();

        // Mock BusinessDiscoveryService to return businesses
        $this->mock(BusinessDiscoveryService::class, function ($mock) {
            $businesses = Business::all();
            $mock->shouldReceive('findBusinesses')
                 ->once()
                 ->andReturn($businesses);
        });

        $scrapingService = app(ScrapingService::class);
        $scrapingService->scrape($scrapingJob);

        // Assert scraping job was completed
        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::COMPLETED, $scrapingJob->status);
        $this->assertEquals(2, $scrapingJob->discovered_businesses_count); // From simulation

        // Assert campaign was updated to PENDING for admin approval
        $campaign->refresh();
        $this->assertEquals(JobNotificationStatusEnum::PENDING, $campaign->status);
        $this->assertGreaterThan(0, $campaign->business_count);
    }

    /** @test */
    public function it_handles_scraping_job_failure_with_retries()
    {
        $scrapingJob = ScrapingJob::factory()->create([
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        // Mock ScrapingService to throw an exception
        $this->mock(ScrapingService::class, function ($mock) {
            $mock->shouldReceive('scrape')
                 ->andThrow(new \Exception('Scraping failed'));
        });

        $job = new ScrapeBusinessesJob($scrapingJob);

        try {
            $job->handle(app(ScrapingService::class));
        } catch (\Exception $e) {
            // Expected to throw
        }

        // Assert scraping job status was updated back to pending for retry
        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::PENDING, $scrapingJob->status);
        $this->assertStringContains('Scraping failed', $scrapingJob->message);
    }

    /** @test */
    public function it_can_cancel_pending_scraping_job_via_api()
    {
        $scrapingJob = ScrapingJob::factory()->create([
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        $response = $this->postJson("/api/admin/scraping-jobs/{$scrapingJob->id}/cancel");

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Scraping job has been cancelled successfully.',
                 ]);

        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::FAILED, $scrapingJob->status);
        $this->assertEquals('Cancelled by admin request', $scrapingJob->message);
    }

    /** @test */
    public function it_can_retry_failed_scraping_job_via_api()
    {
        $scrapingJob = ScrapingJob::factory()->create([
            'status' => ScrapingJobStatusEnum::FAILED,
        ]);

        $response = $this->postJson("/api/admin/scraping-jobs/{$scrapingJob->id}/retry");

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Scraping job has been queued for retry.',
                 ]);

        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::PENDING, $scrapingJob->status);

        // Assert ScrapeBusinessesJob was dispatched again
        Queue::assertPushed(ScrapeBusinessesJob::class);
    }

    /** @test */
    public function it_lists_scraping_jobs_with_filters_and_pagination()
    {
        ScrapingJob::factory()->count(5)->create(['status' => ScrapingJobStatusEnum::PENDING]);
        ScrapingJob::factory()->count(3)->create(['status' => ScrapingJobStatusEnum::COMPLETED]);

        // Test filtering by status
        $response = $this->getJson('/api/admin/scraping-jobs?status=pending&limit=10&page=1');

        $response->assertStatus(200)
                 ->assertJsonCount(5, 'data');

        // Test search functionality
        $scrapingJob = ScrapingJob::factory()->create([
            'location_query' => 'New York',
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        $response = $this->getJson('/api/admin/scraping-jobs?search=New York');

        $response->assertStatus(200)
                 ->assertJsonFragment(['location_query' => 'New York']);
    }

    /** @test */
    public function it_shows_scraping_job_with_campaign_details()
    {
        $campaign = JobNotificationCampaign::factory()->create();
        $scrapingJob = ScrapingJob::factory()->create([
            'job_notification_campaign_id' => $campaign->id,
        ]);

        $response = $this->getJson("/api/admin/scraping-jobs/{$scrapingJob->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'id',
                         'status',
                         'status_label',
                         'can_cancel',
                         'can_retry',
                         'campaign' => [
                             'id',
                             'job_id',
                             'job_title',
                             'status',
                         ],
                     ],
                 ]);
    }
}
