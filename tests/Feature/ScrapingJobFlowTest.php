<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\JobNotificationCampaign;
use App\Models\ScrapingJob;
use App\Models\Business;
use App\Jobs\ProcessJobNotificationJob;
use App\Jobs\ScrapeBusinessesJob;
use App\Services\ScrapingService;
use App\Services\JobNotificationService;
use App\Services\BusinessDiscoveryService;
use App\Services\BusinessDataCompletenessService;
use App\Jobs\EnhanceBusinessDataJob;
use App\Enums\JobNotificationStatusEnum;
use App\Enums\ScrapingJobStatusEnum;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Mockery;

class ScrapingJobFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Mail::fake();
        Queue::fake();
    }

    /** @test */
    public function it_creates_scraping_job_when_no_businesses_found()
    {
        // Mock BusinessDiscoveryService to return no businesses
        $this->mock(BusinessDiscoveryService::class, function ($mock) {
            $mock->shouldReceive('findBusinesses')
                 ->once()
                 ->andReturn(collect([]));
        });

        $eventData = [
            'event_id' => 'test-event-123',
            'job_data' => [
                'job_id' => 'test-job-123',
                'title' => 'Test Cleaning Job',
                'description' => 'Test job description',
                'zip_code' => '12345',
                'category_id' => 'cleaning',
                'budget' => 100.00,
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
            ]
        ];

        $job = new ProcessJobNotificationJob($eventData);
        $job->handle(
            app(BusinessDiscoveryService::class),
            app(JobNotificationService::class)
        );

        // Assert campaign was created with PENDING_SCRAPING status
        $this->assertDatabaseHas('job_notification_campaigns', [
            'job_id' => 'test-job-123',
            'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
            'business_count' => 0,
        ]);

        // Assert scraping job was created
        $this->assertDatabaseHas('scraping_jobs', [
            'location_query' => '12345',
            'category_query' => 'cleaning',
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        // Assert ScrapeBusinessesJob was dispatched
        Queue::assertPushed(ScrapeBusinessesJob::class);
    }

    /** @test */
    public function it_processes_campaign_after_successful_scraping()
    {
        // Create a campaign and scraping job
        $campaign = JobNotificationCampaign::factory()->create([
            'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
            'business_count' => 0,
        ]);

        $scrapingJob = ScrapingJob::factory()->create([
            'job_notification_campaign_id' => $campaign->id,
            'status' => ScrapingJobStatusEnum::IN_PROGRESS,
        ]);

        // Create some businesses that would be found after scraping
        Business::factory()->count(3)->create();

        // Mock BusinessDiscoveryService to return businesses
        $this->mock(BusinessDiscoveryService::class, function ($mock) {
            $businesses = Business::all();
            $mock->shouldReceive('findBusinesses')
                 ->once()
                 ->andReturn($businesses);
        });

        $scrapingService = app(ScrapingService::class);
        $scrapingService->scrape($scrapingJob);

        // Assert scraping job was completed
        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::COMPLETED, $scrapingJob->status);
        $this->assertEquals(2, $scrapingJob->discovered_businesses_count); // From simulation

        // Assert campaign was updated to PENDING for admin approval
        $campaign->refresh();
        $this->assertEquals(JobNotificationStatusEnum::PENDING, $campaign->status);
        $this->assertGreaterThan(0, $campaign->business_count);
    }

    /** @test */
    public function it_handles_scraping_job_failure_with_retries()
    {
        $scrapingJob = ScrapingJob::factory()->create([
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        // Mock ScrapingService to throw an exception
        $this->mock(ScrapingService::class, function ($mock) {
            $mock->shouldReceive('scrape')
                 ->andThrow(new \Exception('Scraping failed'));
        });

        $job = new ScrapeBusinessesJob($scrapingJob);

        try {
            $job->handle(app(ScrapingService::class));
        } catch (\Exception $e) {
            // Expected to throw
        }

        // Assert scraping job status was updated back to pending for retry
        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::PENDING, $scrapingJob->status);
        $this->assertStringContains('Scraping failed', $scrapingJob->message);
    }

    /** @test */
    public function it_can_cancel_pending_scraping_job_via_api()
    {
        $scrapingJob = ScrapingJob::factory()->create([
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        $response = $this->postJson("/api/admin/scraping-jobs/{$scrapingJob->id}/cancel");

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Scraping job has been cancelled successfully.',
                 ]);

        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::FAILED, $scrapingJob->status);
        $this->assertEquals('Cancelled by admin request', $scrapingJob->message);
    }

    /** @test */
    public function it_can_retry_failed_scraping_job_via_api()
    {
        $scrapingJob = ScrapingJob::factory()->create([
            'status' => ScrapingJobStatusEnum::FAILED,
        ]);

        $response = $this->postJson("/api/admin/scraping-jobs/{$scrapingJob->id}/retry");

        $response->assertStatus(200)
                 ->assertJson([
                     'success' => true,
                     'message' => 'Scraping job has been queued for retry.',
                 ]);

        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::PENDING, $scrapingJob->status);

        // Assert ScrapeBusinessesJob was dispatched again
        Queue::assertPushed(ScrapeBusinessesJob::class);
    }

    /** @test */
    public function it_lists_scraping_jobs_with_filters_and_pagination()
    {
        ScrapingJob::factory()->count(5)->create(['status' => ScrapingJobStatusEnum::PENDING]);
        ScrapingJob::factory()->count(3)->create(['status' => ScrapingJobStatusEnum::COMPLETED]);

        // Test filtering by status
        $response = $this->getJson('/api/admin/scraping-jobs?status=pending&limit=10&page=1');

        $response->assertStatus(200)
                 ->assertJsonCount(5, 'data');

        // Test search functionality
        $scrapingJob = ScrapingJob::factory()->create([
            'location_query' => 'New York',
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        $response = $this->getJson('/api/admin/scraping-jobs?search=New York');

        $response->assertStatus(200)
                 ->assertJsonFragment(['location_query' => 'New York']);
    }

    /** @test */
    public function it_shows_scraping_job_with_campaign_details()
    {
        $campaign = JobNotificationCampaign::factory()->create();
        $scrapingJob = ScrapingJob::factory()->create([
            'job_notification_campaign_id' => $campaign->id,
        ]);

        $response = $this->getJson("/api/admin/scraping-jobs/{$scrapingJob->id}");

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         'id',
                         'status',
                         'status_label',
                         'can_cancel',
                         'can_retry',
                         'campaign' => [
                             'id',
                             'job_id',
                             'job_title',
                             'status',
                         ],
                     ],
                 ]);
    }

    /** @test */
    public function it_checks_business_data_completeness()
    {
        // Create businesses with different completeness levels
        $completeBusinesses = Business::factory()->count(2)->create([
            'email' => '<EMAIL>',
            'lat' => '40.7128',
            'lng' => '-74.0060',
            'address' => '123 Main St, New York, NY 10001',
            'name' => 'Complete Business',
        ]);

        $incompleteBusinesses = Business::factory()->count(3)->create([
            'email' => null, // Missing email
            'lat' => null,   // Missing coordinates
            'lng' => null,
            'address' => '456 Elm St', // Missing zip code
            'name' => 'Incomplete Business',
        ]);

        $completenessService = app(BusinessDataCompletenessService::class);
        $allBusinesses = Business::all();

        // Test filtering
        $complete = $completenessService->filterCompleteBusinesses($allBusinesses);
        $incomplete = $completenessService->filterIncompleteBusinesses($allBusinesses);

        $this->assertEquals(2, $complete->count());
        $this->assertEquals(3, $incomplete->count());

        // Test individual business completeness
        $this->assertTrue($completeBusinesses->first()->hasCompleteData());
        $this->assertFalse($incompleteBusinesses->first()->hasCompleteData());

        // Test missing fields detection
        $missingFields = $incompleteBusinesses->first()->getMissingCriticalFields();
        $this->assertContains('email', $missingFields);
        $this->assertContains('lat', $missingFields);
        $this->assertContains('lng', $missingFields);
    }

    /** @test */
    public function it_triggers_enhancement_when_incomplete_businesses_found()
    {
        // Create incomplete businesses in the area
        Business::factory()->count(3)->create([
            'email' => null,
            'lat' => null,
            'lng' => null,
            'address' => '123 Main St, 12345', // Has zip code but missing other fields
            'name' => 'Incomplete Business',
            'location' => 'Test City, 12345', // This will help generate zip_code
        ]);

        // Mock BusinessDiscoveryService to return incomplete businesses
        $this->mock(BusinessDiscoveryService::class, function ($mock) {
            $businesses = Business::all();
            $mock->shouldReceive('findBusinesses')
                 ->once()
                 ->andReturn($businesses->take(0)); // No complete businesses found (empty Eloquent Collection)

            $mock->shouldReceive('findBusinessesNeedingEnhancement')
                 ->once()
                 ->andReturn($businesses); // Return incomplete businesses for enhancement

            $mock->shouldReceive('getDiscoveryRecommendations')
                 ->once()
                 ->andReturn([
                     'analysis' => ['total_businesses' => 3, 'complete_businesses' => 0],
                     'recommendations' => [['type' => 'enhance_all_businesses']]
                 ]);
        });

        $eventData = [
            'event_id' => 'test-event-123',
            'job_data' => [
                'job_id' => 'test-job-123',
                'title' => 'Test Cleaning Job',
                'description' => 'Test job description',
                'zip_code' => '12345',
                'category_id' => 'cleaning',
                'budget' => 100.00,
                'customer_name' => 'John Doe',
                'customer_email' => '<EMAIL>',
            ]
        ];

        $job = new ProcessJobNotificationJob($eventData);
        $job->handle(
            app(BusinessDiscoveryService::class),
            app(JobNotificationService::class),
            app(BusinessDataCompletenessService::class)
        );

        // Assert campaign was created with PENDING_SCRAPING status for enhancement
        $this->assertDatabaseHas('job_notification_campaigns', [
            'job_id' => 'test-job-123',
            'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
        ]);

        // Assert scraping job was created for enhancement
        $this->assertDatabaseHas('scraping_jobs', [
            'location_query' => '12345',
            'category_query' => 'cleaning',
            'status' => ScrapingJobStatusEnum::PENDING,
        ]);

        Queue::assertPushed(ScrapeBusinessesJob::class);
    }

    /** @test */
    public function it_enhances_existing_business_data()
    {
        // Create a business with incomplete data
        $business = Business::factory()->create([
            'name' => 'Test Business',
            'email' => null,
            'lat' => null,
            'lng' => null,
            'address' => '123 Main St', // Missing zip code
            'phone' => null,
        ]);

        $fieldsToEnhance = ['email', 'lat', 'lng', 'address', 'phone'];

        // Create and run the enhancement job
        $job = new EnhanceBusinessDataJob($business, $fieldsToEnhance);
        $job->handle(
            app(BusinessDataCompletenessService::class),
            app(ScrapingService::class)
        );

        // Refresh the business and check if data was enhanced
        $business->refresh();

        $this->assertNotNull($business->email);
        $this->assertNotNull($business->lat);
        $this->assertNotNull($business->lng);
        $this->assertNotNull($business->phone);
        $this->assertStringContainsString(',', $business->address); // Should have zip code added
    }

    /** @test */
    public function it_processes_scraping_with_enhancement_logic()
    {
        // Create incomplete businesses
        $incompleteBusinesses = Business::factory()->count(2)->create([
            'email' => null,
            'lat' => null,
            'lng' => null,
            'address' => '123 Main St',
            'name' => 'Incomplete Business',
        ]);

        $campaign = JobNotificationCampaign::factory()->create([
            'status' => JobNotificationStatusEnum::PENDING_SCRAPING,
            'job_zip_code' => '12345',
            'job_category' => 'cleaning',
        ]);

        $scrapingJob = ScrapingJob::factory()->create([
            'job_notification_campaign_id' => $campaign->id,
            'status' => ScrapingJobStatusEnum::PENDING,
            'location_query' => '12345',
            'category_query' => 'cleaning',
        ]);

        // Mock the scraping service to find existing businesses
        $this->mock(ScrapingService::class, function ($mock) use ($incompleteBusinesses) {
            $mock->shouldReceive('scrape')
                 ->once()
                 ->andReturnUsing(function ($scrapingJob) use ($incompleteBusinesses) {
                     // Simulate the enhancement process
                     $scrapingJob->update([
                         'discovered_businesses_count' => $incompleteBusinesses->count(),
                         'status' => ScrapingJobStatusEnum::COMPLETED,
                         'message' => 'Enhanced existing businesses',
                     ]);
                 });
        });

        $job = new ScrapeBusinessesJob($scrapingJob);
        $job->handle(app(ScrapingService::class));

        // Assert scraping job was completed
        $scrapingJob->refresh();
        $this->assertEquals(ScrapingJobStatusEnum::COMPLETED, $scrapingJob->status);
        $this->assertEquals(2, $scrapingJob->discovered_businesses_count);
    }

    /** @test */
    public function it_analyzes_business_completeness_in_area()
    {
        // Create mixed businesses with proper zip codes in location/address
        Business::factory()->count(3)->create([
            'email' => '<EMAIL>',
            'lat' => '40.7128',
            'lng' => '-74.0060',
            'address' => '123 Main St, New York, NY 12345',
            'location' => 'New York, NY 12345',
            'name' => 'Complete Business',
        ]);

        Business::factory()->count(2)->create([
            'email' => null,
            'lat' => null,
            'lng' => null,
            'address' => '456 Elm St, 12345',
            'location' => 'Test City, 12345',
            'name' => 'Incomplete Business',
        ]);

        $completenessService = app(BusinessDataCompletenessService::class);
        $allBusinesses = Business::all();

        // Test the completeness service directly
        $completeBusinesses = $completenessService->filterCompleteBusinesses($allBusinesses);
        $incompleteBusinesses = $completenessService->filterIncompleteBusinesses($allBusinesses);

        $this->assertEquals(5, $allBusinesses->count());
        $this->assertEquals(3, $completeBusinesses->count());
        $this->assertEquals(2, $incompleteBusinesses->count());

        // Test individual business completeness
        $firstComplete = $completeBusinesses->first();
        $this->assertTrue($firstComplete->hasCompleteData());
        $this->assertTrue($firstComplete->canReceiveNotifications());
        $this->assertTrue($firstComplete->hasValidCoordinates());

        $firstIncomplete = $incompleteBusinesses->first();
        $this->assertFalse($firstIncomplete->hasCompleteData());
        $this->assertFalse($firstIncomplete->canReceiveNotifications());
        $this->assertFalse($firstIncomplete->hasValidCoordinates());
    }

    /** @test */
    public function it_demonstrates_complete_enhanced_flow()
    {
        // Step 1: Create some incomplete businesses in the area
        $incompleteBusinesses = Business::factory()->count(2)->create([
            'email' => null, // Missing critical field
            'lat' => null,   // Missing critical field
            'lng' => null,   // Missing critical field
            'address' => '123 Main St, 12345',
            'location' => 'Test City, 12345',
            'name' => 'Incomplete Business',
            'phone' => null,
        ]);

        // Step 2: Verify businesses are incomplete
        $completenessService = app(BusinessDataCompletenessService::class);
        foreach ($incompleteBusinesses as $business) {
            $this->assertFalse($business->hasCompleteData());
            $missingFields = $business->getMissingCriticalFields();
            $this->assertContains('email', $missingFields);
            $this->assertContains('lat', $missingFields);
            $this->assertContains('lng', $missingFields);
        }

        // Step 3: Test enhancement job
        $business = $incompleteBusinesses->first();
        $originalEmail = $business->email;
        $originalLat = $business->lat;
        $originalLng = $business->lng;

        $enhancementJob = new EnhanceBusinessDataJob($business, ['email', 'lat', 'lng', 'phone']);
        $enhancementJob->handle(
            app(BusinessDataCompletenessService::class),
            app(ScrapingService::class)
        );

        // Step 4: Verify enhancement worked
        $business->refresh();
        $this->assertNotEquals($originalEmail, $business->email);
        $this->assertNotEquals($originalLat, $business->lat);
        $this->assertNotEquals($originalLng, $business->lng);
        $this->assertNotNull($business->email);
        $this->assertNotNull($business->lat);
        $this->assertNotNull($business->lng);
        $this->assertNotNull($business->phone);

        // Step 5: Verify business is now more complete
        $this->assertTrue($business->canReceiveNotifications());
        $this->assertTrue($business->hasValidCoordinates());

        // Check completeness score improved
        $completenessScore = $business->getCompletenessScore();
        $this->assertGreaterThan(50, $completenessScore);

        Log::info('Enhanced business flow test completed successfully', [
            'business_id' => $business->id,
            'completeness_score' => $completenessScore,
            'enhanced_fields' => ['email', 'lat', 'lng', 'phone'],
        ]);
    }
}